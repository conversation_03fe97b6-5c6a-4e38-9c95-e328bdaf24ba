"""
Technical Indicators Module

This module provides functions for calculating and visualizing technical indicators.
"""

import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots


def calculate_moving_averages(data, short_window=20, long_window=50):
    """
    Calculate moving averages for the given data.

    Args:
        data: DataFrame containing price data
        short_window: Window size for short-term moving average
        long_window: Window size for long-term moving average

    Returns:
        DataFrame with moving averages added
    """
    # Make a copy to avoid modifying the original data
    df = data.copy()

    # Calculate moving averages
    df[f'MA{short_window}'] = df['Close'].rolling(window=short_window).mean()
    df[f'MA{long_window}'] = df['Close'].rolling(window=long_window).mean()

    # Identify golden crosses and death crosses
    df['Golden Cross'] = (df[f'MA{short_window}'] > df[f'MA{long_window}']) & (df[f'MA{short_window}'].shift(1) <= df[f'MA{long_window}'].shift(1))
    df['Death Cross'] = (df[f'MA{short_window}'] < df[f'MA{long_window}']) & (df[f'MA{short_window}'].shift(1) >= df[f'MA{long_window}'].shift(1))

    return df


def calculate_rsi(data, window=14):
    """
    Calculate Relative Strength Index (RSI) for the given data.

    Args:
        data: DataFrame containing price data
        window: Window size for RSI calculation

    Returns:
        DataFrame with RSI added
    """
    # Make a copy to avoid modifying the original data
    df = data.copy()

    # Calculate price changes
    delta = df['Close'].diff()

    # Separate gains and losses
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    # Calculate average gain and loss
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()

    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    df['RSI'] = 100 - (100 / (1 + rs))

    # Identify overbought and oversold conditions
    df['Overbought'] = df['RSI'] > 70
    df['Oversold'] = df['RSI'] < 30

    return df


def calculate_bollinger_bands(data, window=20, num_std=2):
    """
    Calculate Bollinger Bands for the given data.

    Args:
        data: DataFrame containing price data
        window: Window size for moving average
        num_std: Number of standard deviations for the bands

    Returns:
        DataFrame with Bollinger Bands added
    """
    # Make a copy to avoid modifying the original data
    df = data.copy()

    # Calculate Bollinger Bands components
    df['BB_middle'] = df['Close'].rolling(window=window).mean()
    df['BB_std'] = df['Close'].rolling(window=window).std()
    df['BB_upper'] = df['BB_middle'] + (df['BB_std'] * num_std)
    df['BB_lower'] = df['BB_middle'] - (df['BB_std'] * num_std)

    # Calculate Bollinger Band width
    df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle']

    # Identify price touches of the bands
    df['BB_upper_touch'] = (df['High'] >= df['BB_upper']) & (df['High'].shift(1) < df['BB_upper'].shift(1))
    df['BB_lower_touch'] = (df['Low'] <= df['BB_lower']) & (df['Low'].shift(1) > df['BB_lower'].shift(1))

    # Identify when price is outside the bands
    df['BB_above_upper'] = df['Close'] > df['BB_upper']
    df['BB_below_lower'] = df['Close'] < df['BB_lower']

    return df


def calculate_macd(data, fast_window=12, slow_window=26, signal_window=9):
    """
    Calculate Moving Average Convergence Divergence (MACD) for the given data.

    Args:
        data: DataFrame containing price data
        fast_window: Window size for fast EMA
        slow_window: Window size for slow EMA
        signal_window: Window size for signal line

    Returns:
        DataFrame with MACD added
    """
    # Make a copy to avoid modifying the original data
    df = data.copy()

    # Calculate MACD components
    df['EMA_fast'] = df['Close'].ewm(span=fast_window, adjust=False).mean()
    df['EMA_slow'] = df['Close'].ewm(span=slow_window, adjust=False).mean()
    df['MACD'] = df['EMA_fast'] - df['EMA_slow']
    df['MACD_signal'] = df['MACD'].ewm(span=signal_window, adjust=False).mean()
    df['MACD_histogram'] = df['MACD'] - df['MACD_signal']

    # Identify MACD crosses
    df['MACD_cross_above'] = (df['MACD'] > df['MACD_signal']) & (df['MACD'].shift(1) <= df['MACD_signal'].shift(1))
    df['MACD_cross_below'] = (df['MACD'] < df['MACD_signal']) & (df['MACD'].shift(1) >= df['MACD_signal'].shift(1))

    return df


def calculate_volume_analysis(data):
    """
    Calculate volume analysis metrics for the given data.

    Args:
        data: DataFrame containing price and volume data

    Returns:
        DataFrame with volume analysis metrics added
    """
    # Make a copy to avoid modifying the original data
    df = data.copy()

    # Calculate average volume
    df['Volume_MA20'] = df['Volume'].rolling(window=20).mean()

    # Identify volume spikes (volume > 2x 20-day average)
    df['Volume_Spike'] = df['Volume'] > 2 * df['Volume_MA20']

    # Calculate price-volume correlation (using 5-day rolling window)
    df['Price_Change'] = df['Close'].pct_change()
    df['Volume_Change'] = df['Volume'].pct_change()

    # Calculate if price and volume are moving in the same direction
    df['Price_Volume_Aligned'] = (df['Price_Change'] > 0) & (df['Volume_Change'] > 0) | (df['Price_Change'] < 0) & (df['Volume_Change'] < 0)

    return df


def create_rsi_figure(data, ticker, surge_period=None, drawdown_period=None):
    """
    Create a figure focused on RSI analysis.

    Args:
        data: DataFrame containing price and volume data
        ticker: Stock ticker symbol
        surge_period: Tuple of (start_date, end_date) for surge period
        drawdown_period: Tuple of (start_date, end_date) for drawdown period

    Returns:
        Plotly figure with RSI analysis
    """
    # Calculate RSI
    rsi_data = calculate_rsi(data)

    # Create figure with single plot - focus only on RSI
    fig = make_subplots(rows=1, cols=1,
                        subplot_titles=("Relative Strength Index (RSI)",))

    # Add RSI
    fig.add_trace(go.Scatter(
        x=rsi_data.index,
        y=rsi_data['RSI'],
        name='RSI',
        line=dict(color='blue', width=2)
    ))

    # Add overbought/oversold lines
    fig.add_trace(go.Scatter(
        x=rsi_data.index,
        y=[70] * len(rsi_data),
        name='Overbought (70)',
        line=dict(color='red', width=1.5, dash='dash'),
        opacity=0.7
    ))

    fig.add_trace(go.Scatter(
        x=rsi_data.index,
        y=[30] * len(rsi_data),
        name='Oversold (30)',
        line=dict(color='green', width=1.5, dash='dash'),
        opacity=0.7
    ))

    # Add markers for overbought and oversold conditions
    overbought_points = rsi_data[rsi_data['Overbought']]
    if not overbought_points.empty:
        fig.add_trace(go.Scatter(
            x=overbought_points.index,
            y=overbought_points['RSI'],
            mode='markers',
            marker=dict(symbol='circle', size=8, color='red'),
            name='Overbought Signal',
            hoverinfo='text',
            hovertext=[f"Overbought: {date.strftime('%Y-%m-%d')} (RSI: {val:.1f})"
                      for date, val in zip(overbought_points.index, overbought_points['RSI'])]
        ))

    oversold_points = rsi_data[rsi_data['Oversold']]
    if not oversold_points.empty:
        fig.add_trace(go.Scatter(
            x=oversold_points.index,
            y=oversold_points['RSI'],
            mode='markers',
            marker=dict(symbol='circle', size=8, color='green'),
            name='Oversold Signal',
            hoverinfo='text',
            hovertext=[f"Oversold: {date.strftime('%Y-%m-%d')} (RSI: {val:.1f})"
                      for date, val in zip(oversold_points.index, oversold_points['RSI'])]
        ))

    # Add surge period if provided
    if surge_period:
        start_date, end_date = surge_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for surge period on RSI chart
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=0,
            y1=100,
            xref="x",
            yref="y",
            fillcolor="rgba(0, 255, 0, 0.2)",  # More visible green
            line=dict(width=2, color="rgba(0, 128, 0, 0.7)"),  # Thicker border
            layer="below",
            opacity=0.7
        )

        # Add annotation for surge period - positioned at top right of the highlighted area
        fig.add_annotation(
            x=end_date,
            y=0.95,
            xref="x",
            yref="paper",
            text="SURGE PERIOD",
            showarrow=False,
            font=dict(color="green", size=14, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="green",
            borderwidth=2,
            borderpad=4,
            align="right"
        )

    # Add drawdown period if provided
    if drawdown_period:
        start_date, end_date = drawdown_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for drawdown period on RSI chart
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=0,
            y1=100,
            xref="x",
            yref="y",
            fillcolor="rgba(255, 0, 0, 0.2)",  # More visible red
            line=dict(width=2, color="rgba(128, 0, 0, 0.7)"),  # Thicker border
            layer="below",
            opacity=0.7
        )

        # Add annotation for drawdown period
        middle_date = start_date + (end_date - start_date) / 2
        fig.add_annotation(
            x=middle_date,
            y=1.12,
            xref="x",
            yref="paper",
            text="DRAWDOWN PERIOD",
            showarrow=False,
            font=dict(color="red", size=16, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.9)",
            bordercolor="red",
            borderwidth=2,
            borderpad=4
        )

    # Update layout
    fig.update_layout(
        title=f"{ticker} RSI Analysis",
        height=500,  # Reduced height since we only have one plot now
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        xaxis_rangeslider_visible=False,
        margin=dict(l=50, r=50, t=100, b=50),
        hovermode="x unified",
        plot_bgcolor='rgba(250,250,250,0.9)'
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="RSI Value", range=[0, 100])

    # Update x-axis with proper date formatting
    fig.update_xaxes(
        title_text="Date",
        tickformat="%Y-%m-%d",
        tickmode="auto",
        nticks=10,
        tickangle=-45,
        showgrid=True
    )

    return fig


def create_macd_figure(data, ticker, surge_period=None, drawdown_period=None):
    """
    Create a figure focused on MACD analysis.

    Args:
        data: DataFrame containing price and volume data
        ticker: Stock ticker symbol
        surge_period: Tuple of (start_date, end_date) for surge period
        drawdown_period: Tuple of (start_date, end_date) for drawdown period

    Returns:
        Plotly figure with MACD analysis
    """
    # Calculate MACD
    macd_data = calculate_macd(data)

    # Create figure with single plot - focus only on MACD
    fig = make_subplots(rows=1, cols=1,
                        subplot_titles=("Moving Average Convergence Divergence (MACD)",))

    # Add MACD
    fig.add_trace(go.Scatter(
        x=macd_data.index,
        y=macd_data['MACD'],
        name='MACD',
        line=dict(color='blue', width=2)
    ))

    # Add MACD signal line
    fig.add_trace(go.Scatter(
        x=macd_data.index,
        y=macd_data['MACD_signal'],
        name='Signal Line',
        line=dict(color='red', width=1.5)
    ))

    # Add MACD histogram
    colors = ['rgba(255,0,0,0.7)' if val < 0 else 'rgba(0,128,0,0.7)' for val in macd_data['MACD_histogram']]
    fig.add_trace(go.Bar(
        x=macd_data.index,
        y=macd_data['MACD_histogram'],
        name='MACD Histogram',
        marker=dict(color=colors)
    ))

    # Add MACD crosses
    macd_cross_above_dates = macd_data[macd_data['MACD_cross_above']].index
    if len(macd_cross_above_dates) > 0:
        macd_cross_above_values = macd_data.loc[macd_cross_above_dates, 'MACD']
        fig.add_trace(go.Scatter(
            x=macd_cross_above_dates,
            y=macd_cross_above_values,
            mode='markers',
            marker=dict(symbol='triangle-up', size=12, color='green', line=dict(width=1, color='darkgreen')),
            name='Bullish Signal',
            hoverinfo='text',
            hovertext=[f"Bullish Signal: {date.strftime('%Y-%m-%d')} (MACD: {val:.4f})"
                      for date, val in zip(macd_cross_above_dates, macd_cross_above_values)]
        ))

    macd_cross_below_dates = macd_data[macd_data['MACD_cross_below']].index
    if len(macd_cross_below_dates) > 0:
        macd_cross_below_values = macd_data.loc[macd_cross_below_dates, 'MACD']
        fig.add_trace(go.Scatter(
            x=macd_cross_below_dates,
            y=macd_cross_below_values,
            mode='markers',
            marker=dict(symbol='triangle-down', size=12, color='red', line=dict(width=1, color='darkred')),
            name='Bearish Signal',
            hoverinfo='text',
            hovertext=[f"Bearish Signal: {date.strftime('%Y-%m-%d')} (MACD: {val:.4f})"
                      for date, val in zip(macd_cross_below_dates, macd_cross_below_values)]
        ))

    # Add surge period if provided
    if surge_period:
        start_date, end_date = surge_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for surge period on MACD chart
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=min(macd_data['MACD_histogram'].min(), macd_data['MACD'].min(), macd_data['MACD_signal'].min()),
            y1=max(macd_data['MACD_histogram'].max(), macd_data['MACD'].max(), macd_data['MACD_signal'].max()),
            xref="x",
            yref="y",
            fillcolor="rgba(0, 255, 0, 0.2)",  # More visible green
            line=dict(width=2, color="rgba(0, 128, 0, 0.7)"),  # Thicker border
            layer="below",
            opacity=0.7
        )

        # Add annotation for surge period
        middle_date = start_date + (end_date - start_date) / 2
        fig.add_annotation(
            x=middle_date,
            y=1.05,
            xref="x",
            yref="paper",
            text="SURGE PERIOD",
            showarrow=False,
            font=dict(color="green", size=16, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.9)",
            bordercolor="green",
            borderwidth=2,
            borderpad=4
        )

    # Add drawdown period if provided
    if drawdown_period:
        start_date, end_date = drawdown_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for drawdown period on MACD chart
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=min(macd_data['MACD_histogram'].min(), macd_data['MACD'].min(), macd_data['MACD_signal'].min()),
            y1=max(macd_data['MACD_histogram'].max(), macd_data['MACD'].max(), macd_data['MACD_signal'].max()),
            xref="x",
            yref="y",
            fillcolor="rgba(255, 0, 0, 0.2)",  # More visible red
            line=dict(width=2, color="rgba(128, 0, 0, 0.7)"),  # Thicker border
            layer="below",
            opacity=0.7
        )

        # Add annotation for drawdown period - positioned at top right of the highlighted area
        fig.add_annotation(
            x=end_date,
            y=0.95,
            xref="x",
            yref="paper",
            text="DRAWDOWN PERIOD",
            showarrow=False,
            font=dict(color="red", size=14, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="red",
            borderwidth=2,
            borderpad=4,
            align="right"
        )

    # Update layout
    fig.update_layout(
        title=f"{ticker} MACD Analysis",
        height=500,  # Reduced height since we only have one plot now
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        xaxis_rangeslider_visible=False,
        margin=dict(l=50, r=50, t=100, b=50),
        hovermode="x unified",
        plot_bgcolor='rgba(250,250,250,0.9)'
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="MACD Value")

    # Update x-axis with proper date formatting
    fig.update_xaxes(
        title_text="Date",
        tickformat="%Y-%m-%d",
        tickmode="auto",
        nticks=10,
        tickangle=-45,
        showgrid=True
    )

    return fig


def create_bollinger_bands_figure(data, ticker, surge_period=None, drawdown_period=None):
    """
    Create a figure focused on Bollinger Bands analysis.

    Args:
        data: DataFrame containing price and volume data
        ticker: Stock ticker symbol
        surge_period: Tuple of (start_date, end_date) for surge period
        drawdown_period: Tuple of (start_date, end_date) for drawdown period

    Returns:
        Plotly figure with Bollinger Bands analysis
    """
    # Calculate Bollinger Bands
    bb_data = calculate_bollinger_bands(data)

    # Create figure with single plot - focus only on Bollinger Bands
    fig = make_subplots(rows=1, cols=1,
                        subplot_titles=("Bollinger Bands Analysis",))

    # Add price candlestick chart
    fig.add_trace(go.Candlestick(
        x=bb_data.index,
        open=bb_data['Open'],
        high=bb_data['High'],
        low=bb_data['Low'],
        close=bb_data['Close'],
        name='Price',
        increasing=dict(line=dict(color='rgba(0,128,0,0.7)', width=1)),
        decreasing=dict(line=dict(color='rgba(255,0,0,0.7)', width=1)),
        showlegend=True
    ))

    # Add Bollinger Bands
    fig.add_trace(go.Scatter(
        x=bb_data.index,
        y=bb_data['BB_middle'],
        name='Middle Band (SMA20)',
        line=dict(color='blue', width=1.5)
    ))

    fig.add_trace(go.Scatter(
        x=bb_data.index,
        y=bb_data['BB_upper'],
        name='Upper Band (+2σ)',
        line=dict(color='rgba(0,128,0,0.7)', width=1, dash='dash')
    ))

    fig.add_trace(go.Scatter(
        x=bb_data.index,
        y=bb_data['BB_lower'],
        name='Lower Band (-2σ)',
        line=dict(color='rgba(255,0,0,0.7)', width=1, dash='dash')
    ))

    # Add upper band touches
    upper_touch_dates = bb_data[bb_data['BB_upper_touch']].index
    if len(upper_touch_dates) > 0:
        upper_touch_prices = bb_data.loc[upper_touch_dates, 'High']
        fig.add_trace(go.Scatter(
            x=upper_touch_dates,
            y=upper_touch_prices,
            mode='markers',
            marker=dict(symbol='triangle-down', size=10, color='green'),
            name='Upper Band Touch',
            hoverinfo='text',
            hovertext=[f"Upper Band Touch: {date.strftime('%Y-%m-%d')} (Potential Reversal)" for date in upper_touch_dates]
        ))

    # Add lower band touches
    lower_touch_dates = bb_data[bb_data['BB_lower_touch']].index
    if len(lower_touch_dates) > 0:
        lower_touch_prices = bb_data.loc[lower_touch_dates, 'Low']
        fig.add_trace(go.Scatter(
            x=lower_touch_dates,
            y=lower_touch_prices,
            mode='markers',
            marker=dict(symbol='triangle-up', size=10, color='red'),
            name='Lower Band Touch',
            hoverinfo='text',
            hovertext=[f"Lower Band Touch: {date.strftime('%Y-%m-%d')} (Potential Reversal)" for date in lower_touch_dates]
        ))

    # Add surge period if provided
    if surge_period:
        start_date, end_date = surge_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for surge period
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=bb_data['Low'].min() * 0.95,  # Extend slightly below the lowest price
            y1=bb_data['High'].max() * 1.05,  # Extend slightly above the highest price
            xref="x",
            yref="y",
            fillcolor="rgba(0, 255, 0, 0.2)",  # Light green
            line=dict(width=2, color="rgba(0, 128, 0, 0.7)"),
            layer="below",
            opacity=0.7
        )

        # Add annotation for surge period
        fig.add_annotation(
            x=end_date,
            y=bb_data['High'].max() * 1.03,
            text="SURGE PERIOD",
            showarrow=False,
            font=dict(color="green", size=14, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="green",
            borderwidth=2,
            borderpad=4,
            align="right"
        )

    # Add drawdown period if provided
    if drawdown_period:
        start_date, end_date = drawdown_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for drawdown period
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=bb_data['Low'].min() * 0.95,  # Extend slightly below the lowest price
            y1=bb_data['High'].max() * 1.05,  # Extend slightly above the highest price
            xref="x",
            yref="y",
            fillcolor="rgba(255, 0, 0, 0.2)",  # Light red
            line=dict(width=2, color="rgba(128, 0, 0, 0.7)"),
            layer="below",
            opacity=0.7
        )

        # Add annotation for drawdown period
        fig.add_annotation(
            x=end_date,
            y=bb_data['Low'].min() * 0.97,
            text="DRAWDOWN PERIOD",
            showarrow=False,
            font=dict(color="red", size=14, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="red",
            borderwidth=2,
            borderpad=4,
            align="right"
        )

    # Update layout
    fig.update_layout(
        title=f"{ticker} Bollinger Bands Analysis",
        height=500,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        xaxis_rangeslider_visible=False,
        margin=dict(l=50, r=50, t=100, b=50),
        hovermode="x unified",
        plot_bgcolor='rgba(250,250,250,0.9)'
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price")

    # Update x-axis with proper date formatting
    fig.update_xaxes(
        title_text="Date",
        tickformat="%Y-%m-%d",
        tickmode="auto",
        nticks=10,
        tickangle=-45,
        showgrid=True
    )

    return fig


def create_volume_figure(data, ticker, surge_period=None, drawdown_period=None):
    """
    Create a figure focused on volume analysis.

    Args:
        data: DataFrame containing price and volume data
        ticker: Stock ticker symbol
        surge_period: Tuple of (start_date, end_date) for surge period
        drawdown_period: Tuple of (start_date, end_date) for drawdown period

    Returns:
        Plotly figure with volume analysis
    """
    # Calculate volume analysis
    volume_data = calculate_volume_analysis(data)

    # Create figure with single plot - focus only on Volume
    fig = make_subplots(rows=1, cols=1,
                        subplot_titles=("Volume Analysis",))

    # Add volume chart with improved styling
    colors = ['rgba(255,0,0,0.7)' if row['Close'] < row['Open'] else 'rgba(0,128,0,0.7)' for _, row in volume_data.iterrows()]
    fig.add_trace(go.Bar(
        x=volume_data.index,
        y=volume_data['Volume'],
        name='Volume',
        marker=dict(color=colors, line=dict(width=0.5, color='rgba(0,0,0,0.3)')),
        showlegend=True
    ))

    # Add volume moving average
    fig.add_trace(go.Scatter(
        x=volume_data.index,
        y=volume_data['Volume_MA20'],
        name='20-Day Avg Volume',
        line=dict(color='purple', width=2),
        showlegend=True
    ))

    # Add volume spikes
    volume_spike_dates = volume_data[volume_data['Volume_Spike']].index
    if len(volume_spike_dates) > 0:
        volume_spike_values = volume_data.loc[volume_spike_dates, 'Volume']
        fig.add_trace(go.Scatter(
            x=volume_spike_dates,
            y=volume_spike_values,
            mode='markers',
            marker=dict(symbol='circle', size=10, color='purple', line=dict(width=1, color='white')),
            name='Volume Spike',
            hoverinfo='text',
            hovertext=[f"Volume Spike: {date.strftime('%Y-%m-%d')} (Volume: {'{:,.0f}'.format(vol)})" for date, vol in zip(volume_spike_dates, volume_spike_values)]
        ))

    # Add price-volume alignment markers
    aligned_dates = volume_data[volume_data['Price_Volume_Aligned']].index
    if len(aligned_dates) > 0:
        aligned_values = volume_data.loc[aligned_dates, 'Volume']
        fig.add_trace(go.Scatter(
            x=aligned_dates,
            y=aligned_values,
            mode='markers',
            marker=dict(symbol='star', size=8, color='blue', line=dict(width=1, color='white')),
            name='Price-Volume Alignment',
            hoverinfo='text',
            hovertext=[f"Price-Volume Alignment: {date.strftime('%Y-%m-%d')}" for date in aligned_dates]
        ))

    # Add surge period if provided
    if surge_period:
        start_date, end_date = surge_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for surge period on volume chart
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=0,
            y1=volume_data['Volume'].max() * 1.1,
            xref="x",
            yref="y",
            fillcolor="rgba(0, 255, 0, 0.2)",  # More visible green
            line=dict(width=2, color="rgba(0, 128, 0, 0.7)"),  # Thicker border
            layer="below",
            opacity=0.7
        )

        # Add annotation for surge period
        middle_date = start_date + (end_date - start_date) / 2
        fig.add_annotation(
            x=middle_date,
            y=1.05,
            xref="x",
            yref="paper",
            text="SURGE PERIOD",
            showarrow=False,
            font=dict(color="green", size=16, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.9)",
            bordercolor="green",
            borderwidth=2,
            borderpad=4
        )

    # Add drawdown period if provided
    if drawdown_period:
        start_date, end_date = drawdown_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for drawdown period on volume chart
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=0,
            y1=volume_data['Volume'].max() * 1.1,
            xref="x",
            yref="y",
            fillcolor="rgba(255, 0, 0, 0.2)",  # More visible red
            line=dict(width=2, color="rgba(128, 0, 0, 0.7)"),  # Thicker border
            layer="below",
            opacity=0.7
        )

        # Add annotation for drawdown period
        middle_date = start_date + (end_date - start_date) / 2
        fig.add_annotation(
            x=middle_date,
            y=1.12,
            xref="x",
            yref="paper",
            text="DRAWDOWN PERIOD",
            showarrow=False,
            font=dict(color="red", size=16, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.9)",
            bordercolor="red",
            borderwidth=2,
            borderpad=4
        )

    # Update layout
    fig.update_layout(
        title=f"{ticker} Volume Analysis",
        height=500,  # Reduced height since we only have one plot now
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        xaxis_rangeslider_visible=False,
        margin=dict(l=50, r=50, t=100, b=50),
        hovermode="x unified",
        plot_bgcolor='rgba(250,250,250,0.9)'
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Volume")

    # Update x-axis with proper date formatting
    fig.update_xaxes(
        title_text="Date",
        tickformat="%Y-%m-%d",
        tickmode="auto",
        nticks=10,
        tickangle=-45,
        showgrid=True
    )

    return fig


def create_price_ma_figure(data, ticker, surge_period=None, drawdown_period=None):
    """
    Create a figure focused only on price action and moving averages.

    Args:
        data: DataFrame containing price and volume data
        ticker: Stock ticker symbol
        surge_period: Tuple of (start_date, end_date) for surge period
        drawdown_period: Tuple of (start_date, end_date) for drawdown period

    Returns:
        Plotly figure with price and moving averages
    """
    # Check if data is empty
    if data.empty:
        print("DEBUG: Data is empty, returning empty figure")
        fig = go.Figure()
        fig.update_layout(
            title=f"No data available for {ticker}",
            annotations=[{
                'text': f"No historical data found for {ticker}.",
                'showarrow': False,
                'font': {'size': 16},
                'x': 0.5,
                'y': 0.5,
                'xref': 'paper',
                'yref': 'paper',
            }]
        )
        return fig

    # Calculate moving averages only
    try:
        ma_data = calculate_moving_averages(data)

        # Combine price and MA data
        df = data.copy()
        df[['MA20', 'MA50', 'Golden Cross', 'Death Cross']] = ma_data[['MA20', 'MA50', 'Golden Cross', 'Death Cross']]
    except Exception as e:
        print(f"DEBUG: Error calculating moving averages: {e}")
        fig = go.Figure()
        fig.update_layout(
            title=f"Error processing data for {ticker}",
            annotations=[{
                'text': f"Error calculating technical indicators: {str(e)}",
                'showarrow': False,
                'font': {'size': 16},
                'x': 0.5,
                'y': 0.5,
                'xref': 'paper',
                'yref': 'paper',
            }]
        )
        return fig

    # Create a single plot for price and MAs
    fig = make_subplots(rows=1, cols=1,
                        subplot_titles=("Price Action and Moving Averages",))

    # Add price candlestick chart
    fig.add_trace(go.Candlestick(
        x=df.index,
        open=df['Open'],
        high=df['High'],
        low=df['Low'],
        close=df['Close'],
        name='Price',
        increasing=dict(line=dict(color='rgba(0,128,0,0.7)', width=1)),
        decreasing=dict(line=dict(color='rgba(255,0,0,0.7)', width=1)),
        showlegend=True
    ))

    # Add moving averages
    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['MA20'],
        name='20-Day MA',
        line=dict(color='blue', width=2)
    ))

    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['MA50'],
        name='50-Day MA',
        line=dict(color='red', width=2)
    ))

    # Add golden crosses
    golden_cross_dates = df[df['Golden Cross']].index
    if len(golden_cross_dates) > 0:
        golden_cross_prices = df.loc[golden_cross_dates, 'Close']
        fig.add_trace(go.Scatter(
            x=golden_cross_dates,
            y=golden_cross_prices,
            mode='markers',
            marker=dict(symbol='star', size=14, color='gold', line=dict(width=1, color='black')),
            name='Golden Cross',
            hoverinfo='text',
            hovertext=[f"Golden Cross: {date.strftime('%Y-%m-%d')} (Bullish Signal)" for date in golden_cross_dates]
        ))

    # Add death crosses
    death_cross_dates = df[df['Death Cross']].index
    if len(death_cross_dates) > 0:
        death_cross_prices = df.loc[death_cross_dates, 'Close']
        fig.add_trace(go.Scatter(
            x=death_cross_dates,
            y=death_cross_prices,
            mode='markers',
            marker=dict(symbol='x', size=14, color='black', line=dict(width=1, color='white')),
            name='Death Cross',
            hoverinfo='text',
            hovertext=[f"Death Cross: {date.strftime('%Y-%m-%d')} (Bearish Signal)" for date in death_cross_dates]
        ))

    # Add surge period if provided
    if surge_period:
        start_date, end_date = surge_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for surge period
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=df['Low'].min() * 0.95,  # Extend slightly below the lowest price
            y1=df['High'].max() * 1.05,  # Extend slightly above the highest price
            xref="x",
            yref="y",
            fillcolor="rgba(0, 255, 0, 0.2)",  # Light green
            line=dict(width=2, color="rgba(0, 128, 0, 0.7)"),
            layer="below",
            opacity=0.7
        )

        # Add annotation for surge period - positioned at top right of the highlighted area
        fig.add_annotation(
            x=end_date,
            y=df['High'].max() * 1.03,
            text="SURGE PERIOD",
            showarrow=False,
            font=dict(color="green", size=14, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="green",
            borderwidth=2,
            borderpad=4,
            align="right"
        )

    # Add drawdown period if provided
    if drawdown_period:
        start_date, end_date = drawdown_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for drawdown period
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=df['Low'].min() * 0.95,  # Extend slightly below the lowest price
            y1=df['High'].max() * 1.05,  # Extend slightly above the highest price
            xref="x",
            yref="y",
            fillcolor="rgba(255, 0, 0, 0.2)",  # Light red
            line=dict(width=2, color="rgba(128, 0, 0, 0.7)"),
            layer="below",
            opacity=0.7
        )

        # Add annotation for drawdown period - positioned at top right of the highlighted area
        fig.add_annotation(
            x=end_date,
            y=df['High'].max() * 0.97,  # Position below the surge period label if both exist
            text="DRAWDOWN PERIOD",
            showarrow=False,
            font=dict(color="red", size=14, family="Arial, sans-serif", weight="bold"),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="red",
            borderwidth=2,
            borderpad=4,
            align="right"
        )

    # Update layout
    fig.update_layout(
        title=f"{ticker} Price Action and Moving Averages",
        height=600,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        xaxis_rangeslider_visible=True,
        margin=dict(l=50, r=50, t=100, b=50),
        hovermode="x unified",
        plot_bgcolor='rgba(250,250,250,0.9)'
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price")

    # Update x-axis with proper date formatting
    fig.update_xaxes(
        title_text="Date",
        tickformat="%Y-%m-%d",
        tickmode="auto",
        nticks=10,
        tickangle=-45,
        showgrid=True
    )

    return fig


def create_technical_indicator_figure(data, ticker, surge_period=None, drawdown_period=None):
    """
    Create a figure with all technical indicators.

    This function is kept for backward compatibility but should be replaced with
    individual indicator functions for better organization.

    Args:
        data: DataFrame containing price and volume data
        ticker: Stock ticker symbol
        surge_period: Tuple of (start_date, end_date) for surge period
        drawdown_period: Tuple of (start_date, end_date) for drawdown period

    Returns:
        Plotly figure with technical indicators
    """
    # Calculate technical indicators
    ma_data = calculate_moving_averages(data)
    rsi_data = calculate_rsi(data)
    macd_data = calculate_macd(data)
    volume_data = calculate_volume_analysis(data)

    # Combine all indicators
    df = data.copy()
    df[['MA20', 'MA50', 'Golden Cross', 'Death Cross']] = ma_data[['MA20', 'MA50', 'Golden Cross', 'Death Cross']]
    df['RSI'] = rsi_data['RSI']
    df['Overbought'] = rsi_data['Overbought']
    df['Oversold'] = rsi_data['Oversold']
    df[['MACD', 'MACD_signal', 'MACD_histogram', 'MACD_cross_above', 'MACD_cross_below']] = macd_data[['MACD', 'MACD_signal', 'MACD_histogram', 'MACD_cross_above', 'MACD_cross_below']]
    df[['Volume_MA20', 'Volume_Spike', 'Price_Volume_Aligned']] = volume_data[['Volume_MA20', 'Volume_Spike', 'Price_Volume_Aligned']]

    # Create subplots with optimized spacing
    fig = make_subplots(rows=4, cols=1,
                        shared_xaxes=True,
                        vertical_spacing=0.12,  # Further increased spacing between subplots
                        row_heights=[0.40, 0.15, 0.15, 0.30],  # Adjusted row heights for better visualization
                        subplot_titles=("Price and Moving Averages", "Volume Analysis", "RSI", "MACD"))

    # Add price candlestick chart with improved styling
    fig.add_trace(go.Candlestick(
        x=df.index,
        open=df['Open'],
        high=df['High'],
        low=df['Low'],
        close=df['Close'],
        name='Price',
        increasing=dict(line=dict(color='rgba(0,128,0,0.7)', width=1)),  # Semi-transparent green
        decreasing=dict(line=dict(color='rgba(255,0,0,0.7)', width=1)),  # Semi-transparent red
        showlegend=True
    ), row=1, col=1)

    # Add moving averages with improved styling
    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['MA20'],
        name='MA20',
        line=dict(color='blue', width=2)  # Thicker line for better visibility
    ), row=1, col=1)

    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['MA50'],
        name='MA50',
        line=dict(color='red', width=2)  # Thicker line for better visibility
    ), row=1, col=1)

    # Add golden crosses with improved styling
    golden_cross_dates = df[df['Golden Cross']].index
    if len(golden_cross_dates) > 0:
        golden_cross_prices = df.loc[golden_cross_dates, 'Close']
        fig.add_trace(go.Scatter(
            x=golden_cross_dates,
            y=golden_cross_prices,
            mode='markers',
            marker=dict(symbol='star', size=14, color='gold', line=dict(width=1, color='black')),  # Improved marker
            name='Golden Cross',
            hoverinfo='text',
            hovertext=[f"Golden Cross: {date.strftime('%Y-%m-%d')} (Bullish Signal)" for date in golden_cross_dates]  # Added signal meaning
        ), row=1, col=1)

    # Add death crosses with improved styling
    death_cross_dates = df[df['Death Cross']].index
    if len(death_cross_dates) > 0:
        death_cross_prices = df.loc[death_cross_dates, 'Close']
        fig.add_trace(go.Scatter(
            x=death_cross_dates,
            y=death_cross_prices,
            mode='markers',
            marker=dict(symbol='x', size=14, color='black', line=dict(width=1, color='white')),  # Improved marker
            name='Death Cross',
            hoverinfo='text',
            hovertext=[f"Death Cross: {date.strftime('%Y-%m-%d')} (Bearish Signal)" for date in death_cross_dates]  # Added signal meaning
        ), row=1, col=1)

    # Add volume chart with improved styling
    colors = ['rgba(255,0,0,0.7)' if row['Close'] < row['Open'] else 'rgba(0,128,0,0.7)' for _, row in df.iterrows()]  # Semi-transparent colors
    fig.add_trace(go.Bar(
        x=df.index,
        y=df['Volume'],
        name='Volume',
        marker=dict(color=colors, line=dict(width=0.5, color='rgba(0,0,0,0.3)')),  # Added subtle border
        showlegend=True  # Show in legend
    ), row=2, col=1)

    # Add volume moving average with improved styling
    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['Volume_MA20'],
        name='Volume MA20',
        line=dict(color='purple', width=2),  # Thicker line
        showlegend=True  # Show in legend
    ), row=2, col=1)

    # Add volume spikes with improved styling
    volume_spike_dates = df[df['Volume_Spike']].index
    if len(volume_spike_dates) > 0:
        volume_spike_values = df.loc[volume_spike_dates, 'Volume']
        fig.add_trace(go.Scatter(
            x=volume_spike_dates,
            y=volume_spike_values,
            mode='markers',
            marker=dict(symbol='circle', size=10, color='purple', line=dict(width=1, color='white')),  # Improved marker
            name='Volume Spike',
            hoverinfo='text',
            hovertext=[f"Volume Spike: {date.strftime('%Y-%m-%d')} (Volume: {'{:,.0f}'.format(vol)})" for date, vol in zip(volume_spike_dates, volume_spike_values)]  # Added volume value
        ), row=2, col=1)

    # Add RSI with improved styling
    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['RSI'],
        name='RSI',
        line=dict(color='blue', width=2),  # Thicker line for better visibility
        showlegend=True  # Show in legend
    ), row=3, col=1)

    # Add overbought/oversold lines with improved styling
    fig.add_trace(go.Scatter(
        x=df.index,
        y=[70] * len(df),
        name='Overbought (70)',  # Added value in name for clarity
        line=dict(color='red', width=1.5, dash='dash'),  # Slightly thicker line
        opacity=0.7,  # Slightly transparent
        showlegend=True  # Show in legend
    ), row=3, col=1)

    fig.add_trace(go.Scatter(
        x=df.index,
        y=[30] * len(df),
        name='Oversold (30)',  # Added value in name for clarity
        line=dict(color='green', width=1.5, dash='dash'),  # Slightly thicker line
        opacity=0.7,  # Slightly transparent
        showlegend=True  # Show in legend
    ), row=3, col=1)

    # Add MACD with improved styling
    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['MACD'],
        name='MACD',
        line=dict(color='blue', width=2),  # Thicker line for better visibility
        showlegend=True  # Show in legend
    ), row=4, col=1)

    fig.add_trace(go.Scatter(
        x=df.index,
        y=df['MACD_signal'],
        name='Signal Line',  # More descriptive name
        line=dict(color='red', width=1.5),  # Slightly thicker line
        showlegend=True  # Show in legend
    ), row=4, col=1)

    # Add MACD histogram with improved styling
    colors = ['rgba(255,0,0,0.7)' if val < 0 else 'rgba(0,128,0,0.7)' for val in df['MACD_histogram']]  # Semi-transparent colors
    fig.add_trace(go.Bar(
        x=df.index,
        y=df['MACD_histogram'],
        name='MACD Histogram',  # More descriptive name
        marker=dict(color=colors),
        showlegend=True  # Show in legend
    ), row=4, col=1)

    # Add MACD crosses with improved styling
    macd_cross_above_dates = df[df['MACD_cross_above']].index
    if len(macd_cross_above_dates) > 0:
        macd_cross_above_values = df.loc[macd_cross_above_dates, 'MACD']
        fig.add_trace(go.Scatter(
            x=macd_cross_above_dates,
            y=macd_cross_above_values,
            mode='markers',
            marker=dict(symbol='triangle-up', size=12, color='green', line=dict(width=1, color='darkgreen')),  # Improved marker
            name='MACD Cross Above',
            hoverinfo='text',
            hovertext=[f"MACD Cross Above: {date.strftime('%Y-%m-%d')} (Bullish Signal)" for date in macd_cross_above_dates]  # Added signal meaning
        ), row=4, col=1)

    macd_cross_below_dates = df[df['MACD_cross_below']].index
    if len(macd_cross_below_dates) > 0:
        macd_cross_below_values = df.loc[macd_cross_below_dates, 'MACD']
        fig.add_trace(go.Scatter(
            x=macd_cross_below_dates,
            y=macd_cross_below_values,
            mode='markers',
            marker=dict(symbol='triangle-down', size=12, color='red', line=dict(width=1, color='darkred')),  # Improved marker
            name='MACD Cross Below',
            hoverinfo='text',
            hovertext=[f"MACD Cross Below: {date.strftime('%Y-%m-%d')} (Bearish Signal)" for date in macd_cross_below_dates]  # Added signal meaning
        ), row=4, col=1)

    # Add surge period if provided with improved styling
    if surge_period:
        start_date, end_date = surge_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for surge period
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=0,
            y1=1,
            xref="x",
            yref="paper",
            fillcolor="rgba(0, 255, 0, 0.1)",  # Light green
            line=dict(width=1, color="rgba(0, 128, 0, 0.5)"),  # Added subtle border
            layer="below",
            opacity=0.5,
            row=1, col=1
        )
        # Add annotation for surge period
        # Calculate the middle point between start and end dates
        middle_date = start_date + (end_date - start_date) / 2
        fig.add_annotation(
            x=middle_date,
            y=1.08,  # Moved higher to avoid overlap
            xref="x",
            yref="paper",
            text="Surge Period",
            showarrow=False,
            font=dict(color="green", size=14, family="Arial, sans-serif"),  # Improved font
            bgcolor="rgba(255, 255, 255, 0.9)",  # More opaque background
            bordercolor="green",
            borderwidth=1,
            borderpad=4,
            row=1, col=1
        )

    # Add drawdown period if provided with improved styling
    if drawdown_period:
        start_date, end_date = drawdown_period
        # Convert to pandas datetime if they're strings
        if isinstance(start_date, str):
            start_date = pd.to_datetime(start_date)
        if isinstance(end_date, str):
            end_date = pd.to_datetime(end_date)

        # Add rectangle for drawdown period
        fig.add_shape(
            type="rect",
            x0=start_date,
            x1=end_date,
            y0=0,
            y1=1,
            xref="x",
            yref="paper",
            fillcolor="rgba(255, 0, 0, 0.1)",  # Light red
            line=dict(width=1, color="rgba(128, 0, 0, 0.5)"),  # Added subtle border
            layer="below",
            opacity=0.5,
            row=1, col=1
        )
        # Add annotation for drawdown period
        # Calculate the middle point between start and end dates
        middle_date = start_date + (end_date - start_date) / 2
        fig.add_annotation(
            x=middle_date,
            y=1.15,  # Moved even higher to avoid overlap with surge period
            xref="x",
            yref="paper",
            text="Drawdown Period",
            showarrow=False,
            font=dict(color="red", size=14, family="Arial, sans-serif"),  # Improved font
            bgcolor="rgba(255, 255, 255, 0.9)",  # More opaque background
            bordercolor="red",
            borderwidth=1,
            borderpad=4,
            row=1, col=1
        )

    # Update layout with improved dimensions
    fig.update_layout(
        title=f"{ticker} Technical Analysis",
        height=1000,  # Further increased height for better visualization
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1,
            font=dict(size=10),  # Smaller font for legend
            itemsizing='constant'  # Consistent legend item size
        ),
        xaxis_rangeslider_visible=False,
        margin=dict(l=70, r=70, t=120, b=70),  # Further increased margins
        hovermode="x unified",
        plot_bgcolor='rgba(250,250,250,0.9)',  # Lighter background for better contrast
        font=dict(size=12)  # Increased font size for better readability
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)
    fig.update_yaxes(title_text="RSI", row=3, col=1)
    fig.update_yaxes(title_text="MACD", row=4, col=1)

    return fig
