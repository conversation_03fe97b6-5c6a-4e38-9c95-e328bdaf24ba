"""
Surge Analyzer Module

This module analyzes stock data to identify significant price surges.
"""

import pandas as pd
import numpy as np
from typing import Dict, Tu<PERSON>, Any


class SurgeAnalyzer:
    """Class for analyzing stock price surges."""

    def __init__(self):
        """Initialize the SurgeAnalyzer."""
        pass

    def calculate_price_change(
        self,
        data: pd.DataFrame,
        price_col: str = 'Adj Close'
    ) -> float:
        """
        Calculate the percentage price change over the entire period.

        Args:
            data: DataFrame containing historical stock data
            price_col: Column name for the price data to use

        Returns:
            Percentage price change
        """
        if data.empty or len(data) < 2:
            return 0.0

        # Check if price_col exists in the DataFrame
        if price_col not in data.columns:
            print(f"Warning: {price_col} column not found in data. Available columns: {data.columns.tolist()}")
            return 0.0

        # Get first and last prices
        try:
            first_price = data[price_col].iloc[0]
            last_price = data[price_col].iloc[-1]

            # Convert to float if needed
            if hasattr(first_price, 'item'):
                first_price = first_price.item()
            elif isinstance(first_price, str):
                # Handle case where first_price is a string (like a ticker symbol)
                print(f"Warning: Price value is a string: '{first_price}'. Cannot convert to float.")
                return 0.0
            else:
                try:
                    first_price = float(first_price)
                except (ValueError, TypeError) as e:
                    print(f"Error converting first price to float: {e}")
                    return 0.0

            if hasattr(last_price, 'item'):
                last_price = last_price.item()
            elif isinstance(last_price, str):
                # Handle case where last_price is a string
                print(f"Warning: Price value is a string: '{last_price}'. Cannot convert to float.")
                return 0.0
            else:
                try:
                    last_price = float(last_price)
                except (ValueError, TypeError) as e:
                    print(f"Error converting last price to float: {e}")
                    return 0.0

            # Calculate percentage change
            if first_price > 0:
                percent_change = ((last_price - first_price) / first_price) * 100
                return round(percent_change, 2)
            else:
                return 0.0

        except Exception as e:
            print(f"Error calculating price change: {e}")
            return 0.0

    def find_surge_stocks(
        self,
        stock_data_dict: Dict[str, pd.DataFrame],
        min_surge_percent: float = 100.0,
        price_col: str = 'Adj Close'
    ) -> Dict[str, float]:
        """
        Find stocks that have surged by at least the specified percentage.

        Args:
            stock_data_dict: Dictionary mapping tickers to their historical data
            min_surge_percent: Minimum percentage increase to qualify as a surge
            price_col: Column name for the price data to use

        Returns:
            Dictionary mapping tickers to their percentage increases
        """
        surge_stocks = {}

        for ticker, data in stock_data_dict.items():
            # Skip empty dataframes
            if data.empty or len(data) < 2:
                print(f"Skipping {ticker}: insufficient data")
                continue

            # Check if the price column exists
            if price_col not in data.columns:
                # Try to find an alternative price column
                alternative_cols = ['Close', 'Adj Close', 'Price']
                found_col = False
                for col in alternative_cols:
                    if col in data.columns:
                        print(f"Using alternative price column '{col}' for {ticker} instead of '{price_col}'")
                        price_col = col
                        found_col = True
                        break

                if not found_col:
                    print(f"Skipping {ticker}: no suitable price column found. Available columns: {data.columns.tolist()}")
                    continue

            try:
                percent_change = self.calculate_price_change(data, price_col)

                if percent_change >= min_surge_percent:
                    surge_stocks[ticker] = percent_change
            except Exception as e:
                print(f"Error analyzing {ticker}: {e}")
                continue

        # Sort by percentage change (descending)
        sorted_surge_stocks = {
            k: v for k, v in sorted(
                surge_stocks.items(),
                key=lambda item: item[1],
                reverse=True
            )
        }

        return sorted_surge_stocks

    def get_max_drawdown(
        self,
        data: pd.DataFrame,
        price_col: str = 'Adj Close'
    ) -> Tuple[float, str, str]:
        """
        Calculate the maximum drawdown for a stock.

        Args:
            data: DataFrame containing historical stock data
            price_col: Column name for the price data to use

        Returns:
            Tuple of (max_drawdown_percentage, peak_date, trough_date)
        """
        if data.empty or len(data) < 2:
            return (0.0, "", "")

        # Calculate the cumulative maximum
        prices = data[price_col]
        rolling_max = prices.cummax()
        drawdown = (prices / rolling_max - 1.0) * 100

        # Find the maximum drawdown
        max_drawdown = drawdown.min()
        if hasattr(max_drawdown, 'item'):
            max_drawdown = max_drawdown.item()

        # Find the trough (lowest point in drawdown)
        trough_idx = drawdown.idxmin()

        # Handle Series objects for trough
        if hasattr(trough_idx, 'iloc'):
            trough_idx = trough_idx.iloc[0] if len(trough_idx) > 0 else trough_idx

        # Find the peak (highest point before the trough)
        # We need to find the highest price before the trough
        if trough_idx in data.index:
            # Get data up to the trough
            data_to_trough = data.loc[:trough_idx]
            if not data_to_trough.empty:
                # Find the peak (highest price before the trough)
                peak_idx = data_to_trough[price_col].idxmax()

                # Handle Series objects for peak
                if hasattr(peak_idx, 'iloc'):
                    peak_idx = peak_idx.iloc[0] if len(peak_idx) > 0 else peak_idx
            else:
                # Fallback if data_to_trough is empty
                peak_idx = data.index[0] if not data.empty else None
        else:
            # Fallback if trough_idx is not in the index
            peak_idx = rolling_max.idxmax()
            if hasattr(peak_idx, 'iloc'):
                peak_idx = peak_idx.iloc[0] if len(peak_idx) > 0 else peak_idx

        # Format dates as strings
        peak_date = str(peak_idx) if peak_idx is not None else ""
        trough_date = str(trough_idx) if trough_idx is not None else ""

        # Try to extract date part if it's a timestamp
        if peak_idx is not None and hasattr(peak_idx, 'date'):
            peak_date = str(peak_idx.date())
        if trough_idx is not None and hasattr(trough_idx, 'date'):
            trough_date = str(trough_idx.date())

        return (round(max_drawdown, 2), peak_date, trough_date)

    def find_best_surge_period(
        self,
        data: pd.DataFrame,
        price_col: str = 'Adj Close',
        min_period_days: int = 30,  # Minimum period length in days
        smoothing_window: int = 5   # Window for smoothing to avoid noise
    ) -> Tuple[pd.Timestamp, pd.Timestamp, float]:
        """
        Find the best surge period within the data.

        Args:
            data: DataFrame containing historical stock data
            price_col: Column name for the price data to use
            min_period_days: Minimum period length in days
            smoothing_window: Window size for price smoothing to reduce noise

        Returns:
            Tuple of (start_date, end_date, percent_change)
        """
        if data.empty:
            # Return empty values for empty DataFrame
            return pd.Timestamp('1970-01-01'), pd.Timestamp('1970-01-01'), 0.0
        elif len(data) < 2:
            # Return the single date twice for DataFrames with only one row
            return data.index[0], data.index[0], 0.0

        # Apply smoothing to reduce noise
        smoothed_prices = data[price_col].rolling(window=smoothing_window, min_periods=1).mean()

        # Find the local minima and maxima in the smoothed price series
        # A point is a local minimum if it's smaller than both its neighbors
        # A point is a local maximum if it's larger than both its neighbors
        local_min_indices = []
        local_max_indices = []

        # Convert to numpy array to avoid pandas Series truth value issues
        prices_array = smoothed_prices.values

        for i in range(1, len(prices_array) - 1):
            if prices_array[i] < prices_array[i-1] and prices_array[i] < prices_array[i+1]:
                local_min_indices.append(i)
            elif prices_array[i] > prices_array[i-1] and prices_array[i] > prices_array[i+1]:
                local_max_indices.append(i)

        # Add the first and last points if they might be extrema
        if len(prices_array) > 1:
            if prices_array[0] < prices_array[1]:
                local_min_indices.insert(0, 0)
            elif prices_array[0] > prices_array[1]:
                local_max_indices.insert(0, 0)

            if prices_array[-1] < prices_array[-2]:
                local_min_indices.append(len(prices_array) - 1)
            elif prices_array[-1] > prices_array[-2]:
                local_max_indices.append(len(prices_array) - 1)

        # Initialize variables to track the best surge
        best_start_idx = 0
        best_end_idx = len(data) - 1
        best_percent_change = -float('inf')

        # If we have local minima, use them as potential starting points
        start_candidates = local_min_indices if local_min_indices else list(range(0, len(data) - min_period_days))

        # Find the best surge period
        for start_idx in start_candidates:
            # Get the price as a scalar value
            start_price = float(smoothed_prices.iloc[start_idx].item())

            # Skip if start price is zero or negative
            if start_price <= 0:
                continue

            # Potential end points are local maxima after the start point
            end_candidates = [idx for idx in local_max_indices if idx > start_idx + min_period_days]

            # If no local maxima found, use all points after minimum period
            if not end_candidates:
                end_candidates = list(range(start_idx + min_period_days, len(data)))

            for end_idx in end_candidates:
                # Get the price as a scalar value
                end_price = float(smoothed_prices.iloc[end_idx].item())

                # Calculate the percentage change
                percent_change = ((end_price - start_price) / start_price) * 100

                # Update if this is the best surge so far
                if percent_change > best_percent_change:
                    best_percent_change = percent_change
                    best_start_idx = start_idx
                    best_end_idx = end_idx

        # Get the actual dates
        best_start_date = data.index[best_start_idx]
        best_end_date = data.index[best_end_idx]

        # If no good surge was found, use the full range
        if best_percent_change <= 0:
            return data.index[0], data.index[-1], self.calculate_price_change(data, price_col)

        # Calculate the actual percentage change using the original price data
        actual_start_price = float(data[price_col].iloc[best_start_idx].item())
        actual_end_price = float(data[price_col].iloc[best_end_idx].item())
        actual_percent_change = ((actual_end_price - actual_start_price) / actual_start_price) * 100

        return best_start_date, best_end_date, actual_percent_change

    def analyze_surge_patterns(
        self,
        data: pd.DataFrame,
        price_col: str = 'Adj Close'
    ) -> Dict[str, Any]:
        """
        Perform detailed analysis of a stock's surge pattern.

        Args:
            data: DataFrame containing historical stock data
            price_col: Column name for the price data to use

        Returns:
            Dictionary with various metrics about the surge pattern
        """
        if data.empty or len(data) < 2:
            return {}

        # Calculate basic metrics for the entire period
        total_change = self.calculate_price_change(data, price_col)
        max_drawdown, peak_date, trough_date = self.get_max_drawdown(data, price_col)

        # Calculate volatility (standard deviation of daily returns)
        daily_returns = data[price_col].pct_change().dropna()
        volatility = round(daily_returns.std() * 100, 2)

        # Calculate annualized return
        days = (data.index[-1] - data.index[0]).days
        years = days / 365.25
        annualized_return = round(((1 + total_change/100) ** (1/years) - 1) * 100, 2)

        # Find the best surge period
        surge_start_date, surge_end_date, surge_percent_change = self.find_best_surge_period(data, price_col)

        # Get the prices at the surge start and end
        # Use iloc[0] to avoid FutureWarning and handle Series objects properly
        if isinstance(data.loc[surge_start_date, price_col], pd.Series):
            surge_start_price = float(data.loc[surge_start_date, price_col].iloc[0])
        else:
            surge_start_price = float(data.loc[surge_start_date, price_col])

        if isinstance(data.loc[surge_end_date, price_col], pd.Series):
            surge_end_price = float(data.loc[surge_end_date, price_col].iloc[0])
        else:
            surge_end_price = float(data.loc[surge_end_date, price_col])

        # Ensure surge_percent_change is a scalar value
        if hasattr(surge_percent_change, 'item'):
            surge_percent_change = surge_percent_change.item()
        elif isinstance(surge_percent_change, (list, np.ndarray)):
            surge_percent_change = float(surge_percent_change[0]) if len(surge_percent_change) > 0 else float(surge_percent_change)
        else:
            surge_percent_change = float(surge_percent_change)

        return {
            'total_change_percent': total_change,
            'annualized_return_percent': annualized_return,
            'max_drawdown_percent': max_drawdown,
            'drawdown_peak_date': peak_date,
            'drawdown_trough_date': trough_date,
            'volatility_percent': volatility,
            'days_analyzed': days,
            'start_date': str(data.index[0].date()),  # Full period start date
            'end_date': str(data.index[-1].date()),   # Full period end date
            'start_price': round(data[price_col].iloc[0], 2),  # Full period start price
            'end_price': round(data[price_col].iloc[-1], 2),   # Full period end price
            'surge_start_date': str(surge_start_date.date()),  # Surge period start date
            'surge_end_date': str(surge_end_date.date()),      # Surge period end date
            'surge_start_price': round(surge_start_price, 2),  # Surge period start price
            'surge_end_price': round(surge_end_price, 2),      # Surge period end price
            'surge_percent_change': round(surge_percent_change, 2)  # Surge period percent change
        }
