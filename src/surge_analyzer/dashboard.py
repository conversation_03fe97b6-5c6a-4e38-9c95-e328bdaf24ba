"""
Dashboard Module

This module provides a web-based dashboard for visualizing stock surge analysis results.
"""

import os
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from dash import Dash, html, dcc, callback, Output, Input
import dash_bootstrap_components as dbc
from datetime import datetime, timedelta
import traceback
from src.surge_analyzer.data.data_fetcher import StockDataFetcher
from src.surge_analyzer.technical_analysis.technical_indicators import (
    create_rsi_figure,
    create_macd_figure,
    create_bollinger_bands_figure,
    create_volume_figure,
    create_price_ma_figure
)


def load_results(results_path):
    """
    Load results from a JSON file.

    Args:
        results_path: Path to the JSON results file

    Returns:
        DataFrame containing the results
    """
    try:
        if results_path.endswith('.csv'):
            return pd.read_csv(results_path)
        elif results_path.endswith('.json'):
            return pd.read_json(results_path)
        else:
            print(f"Unsupported file format: {results_path}")
            return pd.DataFrame()
    except Exception as e:
        print(f"Error loading results: {e}")
        return pd.DataFrame()


def get_latest_results_file(results_dir='results/surge_analyzer'):
    """
    Get the path to the latest results file.

    Args:
        results_dir: Directory containing results files

    Returns:
        Path to the latest results file
    """
    try:
        # Get all JSON files in the results directory
        json_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]

        if not json_files:
            # If no JSON files, try CSV files
            csv_files = [f for f in os.listdir(results_dir) if f.endswith('.csv')]
            if not csv_files:
                print(f"No results files found in {results_dir}")
                return None

            # Get the latest CSV file
            latest_file = max(csv_files, key=lambda f: os.path.getmtime(os.path.join(results_dir, f)))
            return os.path.join(results_dir, latest_file)

        # Get the latest JSON file
        latest_file = max(json_files, key=lambda f: os.path.getmtime(os.path.join(results_dir, f)))
        return os.path.join(results_dir, latest_file)
    except Exception as e:
        print(f"Error getting latest results file: {e}")
        return None


# Create a global instance of StockDataFetcher
_data_fetcher = StockDataFetcher(data_dir="data/surge_analyzer")

def fetch_stock_data(ticker, start_date=None, end_date=None):
    """
    Fetch historical stock data for a given ticker.

    Args:
        ticker: Stock ticker symbol
        start_date: Start date for historical data (optional)
        end_date: End date for historical data (optional)

    Returns:
        DataFrame containing historical stock data
    """
    print(f"\n=== Fetching stock data for {ticker} ===")

    try:
        # Always use current date as end date
        end_date_actual = pd.Timestamp.now()

        # Use 3 years of data by default
        start_date_actual = end_date_actual - pd.Timedelta(days=365*3)

        print(f"Using date range: {start_date_actual} to {end_date_actual}")

        # Use yfinance to get the data
        import yfinance as yf

        # Create ticker object
        stock = yf.Ticker(ticker)

        # Fetch the historical data
        print(f"Downloading data for {ticker}...")
        data = stock.history(period="3y")
        print(f"Downloaded {len(data)} rows of data")

        if data.empty:
            print(f"No data found for {ticker}, trying with a different period")
            data = stock.history(period="5y")
            print(f"Downloaded {len(data)} rows of data with longer period")

        if not data.empty:
            # Print data information
            print(f"Data shape: {data.shape}")
            print(f"Data columns: {data.columns}")
            print(f"Data index range: {data.index.min()} to {data.index.max()}")
            print(f"First few rows:\n{data.head()}")

            # Save the data to a CSV file for future use
            data_dir = "data/surge_analyzer"
            os.makedirs(data_dir, exist_ok=True)
            csv_filename = os.path.join(data_dir, f"{ticker}_data.csv")
            data.to_csv(csv_filename)
            print(f"Saved data to {csv_filename}")

            return data
        else:
            print(f"Could not fetch data for {ticker}")
            return pd.DataFrame()
    except Exception as e:
        print(f"Error fetching stock data for {ticker}: {e}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()


def create_moving_average_chart(ticker, data=None, surge_info=None):
    """Create a chart showing moving averages and MACD for a stock.

    Args:
        ticker (str): The stock ticker symbol
        data (pd.DataFrame, optional): Stock price data. If None, data will be loaded.
        surge_info (dict, optional): Dictionary with surge information

    Returns:
        plotly.graph_objects.Figure: A figure showing moving averages and MACD
    """
    # Load data if not provided
    if data is None:
        # Load the stock data
        try:
            data_path = os.path.join('data/surge_analyzer', 'stocks', f"{ticker}.csv")
            data = pd.read_csv(data_path, index_col='Date', parse_dates=True)
        except FileNotFoundError:
            # Return empty figure if data not found
            return go.Figure().update_layout(
                title=f"No data available for {ticker}",
                xaxis_title="Date",
                yaxis_title="Price",
                template="plotly_white"
            )

    # Make sure we have enough data
    if len(data) < 200:  # Need at least 200 days for 200-day MA
        return go.Figure().update_layout(
            title=f"Insufficient data for {ticker} moving average analysis",
            xaxis_title="Date",
            yaxis_title="Price",
            template="plotly_white",
            annotations=[{
                'text': "Need at least 200 days of data for complete moving average analysis",
                'showarrow': False,
                'font': {'size': 16},
                'xref': 'paper',
                'yref': 'paper',
                'x': 0.5,
                'y': 0.5
            }]
        )

    # Create the figure with subplots for better organization
    fig = make_subplots(rows=3, cols=1, shared_xaxes=True,
                       vertical_spacing=0.05,  # Reduce spacing between subplots
                       row_heights=[0.475, 0.475, 0.05],  # Equal size for price chart and MACD, with range slider
                       subplot_titles=("", "", ""))  # Empty titles to create more space for the plots

    # Add candlestick chart
    fig.add_trace(go.Candlestick(
        x=data.index,
        open=data['Open'],
        high=data['High'],
        low=data['Low'],
        close=data['Close'],
        name='Price',
        showlegend=True
    ), row=1, col=1)

    # Calculate and add moving averages
    # 20-day SMA
    sma20 = data['Close'].rolling(window=20).mean()
    fig.add_trace(go.Scatter(
        x=data.index,
        y=sma20,
        mode='lines',
        name='20-Day SMA',
        line=dict(color='blue', width=1.5)
    ), row=1, col=1)

    # 50-day SMA
    sma50 = data['Close'].rolling(window=50).mean()
    fig.add_trace(go.Scatter(
        x=data.index,
        y=sma50,
        mode='lines',
        name='50-Day SMA',
        line=dict(color='orange', width=1.5)
    ), row=1, col=1)

    # 200-day SMA
    sma200 = data['Close'].rolling(window=200).mean()
    fig.add_trace(go.Scatter(
        x=data.index,
        y=sma200,
        mode='lines',
        name='200-Day SMA',
        line=dict(color='red', width=1.5)
    ), row=1, col=1)

    # Add EMA
    ema20 = data['Close'].ewm(span=20, adjust=False).mean()
    fig.add_trace(go.Scatter(
        x=data.index,
        y=ema20,
        mode='lines',
        name='20-Day EMA',
        line=dict(color='purple', width=1.5, dash='dash')
    ), row=1, col=1)

    # Calculate and add MACD (Moving Average Convergence Divergence)
    # MACD Line = 12-period EMA - 26-period EMA
    ema12 = data['Close'].ewm(span=12, adjust=False).mean()
    ema26 = data['Close'].ewm(span=26, adjust=False).mean()
    macd = ema12 - ema26

    # Signal Line = 9-period EMA of MACD Line
    signal = macd.ewm(span=9, adjust=False).mean()

    # MACD Histogram = MACD Line - Signal Line
    histogram = macd - signal

    # Add MACD line
    fig.add_trace(go.Scatter(
        x=data.index,
        y=macd,
        mode='lines',
        name='MACD',
        line=dict(color='blue', width=1.5)
    ), row=2, col=1)

    # Add Signal line
    fig.add_trace(go.Scatter(
        x=data.index,
        y=signal,
        mode='lines',
        name='Signal',
        line=dict(color='red', width=1.5)
    ), row=2, col=1)

    # Add Histogram as bar chart
    colors = ['rgba(0, 180, 0, 0.7)' if val >= 0 else 'rgba(180, 0, 0, 0.7)' for val in histogram]

    fig.add_trace(go.Bar(
        x=data.index,
        y=histogram,
        name='Histogram',
        marker=dict(color=colors, line=dict(color='rgba(0,0,0,0.1)', width=0.5))
    ), row=2, col=1)

    # Add zero line for MACD
    fig.add_trace(go.Scatter(
        x=data.index,
        y=[0] * len(data.index),
        mode='lines',
        line=dict(color='gray', width=1, dash='dash'),
        showlegend=False,
        hoverinfo='none'
    ), row=2, col=1)

    # Find crossovers
    # Detect when 50-day SMA crosses above 200-day SMA (Golden Cross)
    golden_cross = (sma50.shift(1) < sma200.shift(1)) & (sma50 > sma200)
    death_cross = (sma50.shift(1) > sma200.shift(1)) & (sma50 < sma200)

    # Add markers for crossovers
    for idx in golden_cross[golden_cross].index:
        if idx in data.index:
            fig.add_trace(go.Scatter(
                x=[idx],
                y=[data.loc[idx, 'Close']],
                mode='markers',
                marker=dict(size=12, color='green', symbol='triangle-up'),
                name='Golden Cross',
                showlegend=True
            ), row=1, col=1)

    for idx in death_cross[death_cross].index:
        if idx in data.index:
            fig.add_trace(go.Scatter(
                x=[idx],
                y=[data.loc[idx, 'Close']],
                mode='markers',
                marker=dict(size=12, color='red', symbol='triangle-down'),
                name='Death Cross',
                showlegend=True
            ), row=1, col=1)

    # Find MACD crossovers (MACD crosses above/below Signal)
    macd_buy = (macd.shift(1) < signal.shift(1)) & (macd > signal)
    macd_sell = (macd.shift(1) > signal.shift(1)) & (macd < signal)

    # Add MACD buy signals to price chart
    buy_signals = []
    for idx in macd_buy[macd_buy].index:
        if idx in data.index:
            buy_signals.append((idx, data.loc[idx, 'Close'] * 0.98))

    if buy_signals:
        fig.add_trace(go.Scatter(
            x=[point[0] for point in buy_signals],
            y=[point[1] for point in buy_signals],
            mode='markers',
            marker=dict(size=8, color='green', symbol='circle'),
            name='MACD Buy Signal',
            showlegend=True
        ), row=1, col=1)

    # Add MACD sell signals to price chart
    sell_signals = []
    for idx in macd_sell[macd_sell].index:
        if idx in data.index:
            sell_signals.append((idx, data.loc[idx, 'Close'] * 1.02))

    if sell_signals:
        fig.add_trace(go.Scatter(
            x=[point[0] for point in sell_signals],
            y=[point[1] for point in sell_signals],
            mode='markers',
            marker=dict(size=8, color='red', symbol='circle'),
            name='MACD Sell Signal',
            showlegend=True
        ), row=1, col=1)

    # Update layout
    fig.update_layout(
        title=f"{ticker} - Moving Average Analysis",
        template="plotly_white",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.10,  # Increased to prevent overlap with plot
            xanchor="center",
            x=0.5,  # Centered for better visibility
            font=dict(size=9),  # Smaller font for legend
            itemsizing='constant',  # Consistent legend item sizes
            tracegroupgap=5,  # Reduce gap between legend groups
            bgcolor="rgba(255, 255, 255, 0.9)"  # Semi-transparent background
        ),
        margin=dict(l=40, r=40, t=80, b=60),  # Increased bottom margin for date labels
        hovermode="x unified",
        height=700  # Ensure consistent height
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="MACD", row=2, col=1)

    # Hide date labels on the upper chart, show only on the lower chart
    fig.update_xaxes(
        tickformat="%b %Y",  # Format dates as 'Jan 2023'
        tickangle=0,  # Straight date labels
        nticks=10,  # Limit number of ticks for cleaner appearance
        automargin=True,  # Ensure date labels are fully visible
        showticklabels=False,  # Hide tick labels on upper chart
        row=1, col=1
    )
    fig.update_xaxes(
        tickformat="%b %Y",  # Format dates as 'Jan 2023'
        tickangle=0,  # Straight date labels
        nticks=10,  # Limit number of ticks for cleaner appearance
        automargin=True,  # Ensure date labels are fully visible
        type="date",  # Explicitly set as date type
        showticklabels=True,  # Show tick labels on lower chart
        row=2, col=1
    )

    # Add surge and drawdown periods if available
    if surge_info is not None and not data.empty:
        # Get the surge period dates from surge_info
        surge_start_date_str = surge_info.get('surge_start_date') or surge_info.get('Surge Start Date')
        surge_end_date_str = surge_info.get('surge_end_date') or surge_info.get('Surge End Date')
        drawdown_peak_date_str = surge_info.get('drawdown_peak_date') or surge_info.get('Drawdown Peak Date')
        drawdown_trough_date_str = surge_info.get('drawdown_trough_date') or surge_info.get('Drawdown Trough Date')

        # Convert dates to datetime objects if they exist
        if surge_start_date_str and surge_end_date_str:
            start_date = pd.to_datetime(surge_start_date_str)
            end_date = pd.to_datetime(surge_end_date_str)

            # Add the surge period rectangle
            fig.add_vrect(
                x0=start_date,
                x1=end_date,
                fillcolor="rgba(0, 200, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(0, 150, 0, 0.5)",
                row=1, col=1
            )

        # Add drawdown period if available
        if drawdown_peak_date_str and drawdown_trough_date_str:
            peak_date = pd.to_datetime(drawdown_peak_date_str)
            trough_date = pd.to_datetime(drawdown_trough_date_str)

            # Add the drawdown rectangle
            fig.add_vrect(
                x0=peak_date,
                x1=trough_date,
                fillcolor="rgba(255, 0, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(200, 0, 0, 0.5)",
                row=1, col=1
            )

    # No need for empty trace since we're using the main x-axis for the range slider

    # Configure the range slider to appear at the bottom with see-through style
    fig.update_layout(
        xaxis2=dict(
            rangeslider=dict(
                visible=True,
                thickness=0.05,  # Thinner slider
                bgcolor="rgba(211, 211, 211, 0.2)",  # Semi-transparent grey
                bordercolor="rgba(0, 0, 0, 0.1)",    # Subtle border
                borderwidth=1,
                yaxis=dict(rangemode="fixed")  # Fix the y-axis range to prevent overlap
            ),
            type="date",
            automargin=True,  # Ensure date labels are fully visible
            tickformat="%b %Y",  # Format dates as 'Jan 2023'
            tickangle=0,  # Straight date labels
            nticks=10,  # Limit number of ticks for cleaner appearance
            showticklabels=True  # Explicitly show tick labels
        ),
        xaxis=dict(
            rangeslider=dict(visible=False),  # Disable rangeslider for first axis
            tickangle=0,  # Straight date labels
            type="date",  # Explicitly set as date type
            automargin=True,  # Ensure date labels are fully visible
            tickformat="%b %Y",  # Format dates as 'Jan 2023'
            nticks=10,  # Limit number of ticks for cleaner appearance
            showticklabels=False  # Hide tick labels on upper chart
        )
    )

    return fig


def create_rsi_chart(ticker, data=None, surge_info=None):
    """Create a chart showing RSI analysis for a stock.

    Args:
        ticker (str): The stock ticker symbol
        data (pd.DataFrame, optional): Stock price data. If None, data will be loaded.

    Returns:
        plotly.graph_objects.Figure: A figure showing RSI analysis
    """
    # Load data if not provided
    if data is None:
        # Load the stock data
        try:
            data_path = os.path.join('data/surge_analyzer', 'stocks', f"{ticker}.csv")
            data = pd.read_csv(data_path, index_col='Date', parse_dates=True)
        except FileNotFoundError:
            # Return empty figure if data not found
            return go.Figure().update_layout(
                title=f"No data available for {ticker}",
                xaxis_title="Date",
                yaxis_title="Price",
                template="plotly_white"
            )

    # Make sure we have enough data
    if len(data) < 30:  # Need at least 30 days for meaningful RSI analysis
        return go.Figure().update_layout(
            title=f"Insufficient data for {ticker} RSI analysis",
            xaxis_title="Date",
            yaxis_title="Price",
            template="plotly_white",
            annotations=[{
                'text': "Need at least 30 days of data for RSI analysis",
                'showarrow': False,
                'font': {'size': 16},
                'xref': 'paper',
                'yref': 'paper',
                'x': 0.5,
                'y': 0.5
            }]
        )

    # Create subplots: price chart and RSI with 50-50 distribution
    fig = make_subplots(rows=2, cols=1, shared_xaxes=True,
                        vertical_spacing=0.1,  # Increase spacing between subplots
                        row_heights=[0.5, 0.5])  # Equal 50-50 distribution

    # Add price chart
    fig.add_trace(go.Candlestick(
        x=data.index,
        open=data['Open'],
        high=data['High'],
        low=data['Low'],
        close=data['Close'],
        name='Price',
        showlegend=True
    ), row=1, col=1)

    # Calculate and add RSI
    # Calculate price changes
    delta = data['Close'].diff()

    # Get gains and losses
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    # Calculate average gain and loss over 14 periods
    avg_gain = gain.rolling(window=14).mean()
    avg_loss = loss.rolling(window=14).mean()

    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Add RSI line
    fig.add_trace(go.Scatter(
        x=data.index,
        y=rsi,
        mode='lines',
        name='RSI (14)',
        line=dict(color='purple', width=1.5)
    ), row=2, col=1)

    # Add overbought/oversold lines with shaded regions
    # Add overbought region (70-100)
    fig.add_trace(go.Scatter(
        x=data.index,
        y=[70] * len(data.index),
        mode='lines',
        line=dict(color='red', width=1, dash='dash'),
        name='Overbought (70)',
        showlegend=True
    ), row=2, col=1)

    # Add oversold region (0-30)
    fig.add_trace(go.Scatter(
        x=data.index,
        y=[30] * len(data.index),
        mode='lines',
        line=dict(color='green', width=1, dash='dash'),
        name='Oversold (30)',
        showlegend=True
    ), row=2, col=1)

    # Add centerline
    fig.add_trace(go.Scatter(
        x=data.index,
        y=[50] * len(data.index),
        mode='lines',
        line=dict(color='gray', width=1, dash='dot'),
        name='Centerline (50)',
        showlegend=True
    ), row=2, col=1)

    # Add shaded regions for overbought and oversold
    # Overbought region (70-100)
    fig.add_trace(go.Scatter(
        x=data.index,
        y=[70] * len(data.index),
        mode='lines',
        line=dict(width=0),
        showlegend=False,
        hoverinfo='none'
    ), row=2, col=1)

    fig.add_trace(go.Scatter(
        x=data.index,
        y=[100] * len(data.index),
        mode='lines',
        line=dict(width=0),
        fill='tonexty',
        fillcolor='rgba(255, 0, 0, 0.1)',
        showlegend=False,
        hoverinfo='none'
    ), row=2, col=1)

    # Oversold region (0-30)
    fig.add_trace(go.Scatter(
        x=data.index,
        y=[0] * len(data.index),
        mode='lines',
        line=dict(width=0),
        showlegend=False,
        hoverinfo='none'
    ), row=2, col=1)

    fig.add_trace(go.Scatter(
        x=data.index,
        y=[30] * len(data.index),
        mode='lines',
        line=dict(width=0),
        fill='tonexty',
        fillcolor='rgba(0, 255, 0, 0.1)',
        showlegend=False,
        hoverinfo='none'
    ), row=2, col=1)

    # Mark overbought and oversold regions
    buy_signals = []
    sell_signals = []

    for i in range(1, len(rsi)):
        # Oversold to normal (buying opportunity)
        if rsi.iloc[i-1] <= 30 and rsi.iloc[i] > 30:
            buy_signals.append((data.index[i], data['Low'].iloc[i] * 0.99))

        # Overbought to normal (selling opportunity)
        if rsi.iloc[i-1] >= 70 and rsi.iloc[i] < 70:
            sell_signals.append((data.index[i], data['High'].iloc[i] * 1.01))

    # Add buy signals
    if buy_signals:
        fig.add_trace(go.Scatter(
            x=[x[0] for x in buy_signals],
            y=[x[1] for x in buy_signals],
            mode='markers',
            marker=dict(size=10, color='green', symbol='triangle-up'),
            name='Buy Signal',
            showlegend=True
        ), row=1, col=1)

    # Add sell signals
    if sell_signals:
        fig.add_trace(go.Scatter(
            x=[x[0] for x in sell_signals],
            y=[x[1] for x in sell_signals],
            mode='markers',
            marker=dict(size=10, color='red', symbol='triangle-down'),
            name='Sell Signal',
            showlegend=True
        ), row=1, col=1)

    # Add RSI divergence analysis
    # Find price highs and lows
    price_highs = []
    price_lows = []
    rsi_highs = []
    rsi_lows = []

    window = 10  # Look for local highs/lows within this window

    for i in range(window, len(data) - window):
        # Check for price highs
        if data['High'].iloc[i] == data['High'].iloc[i-window:i+window+1].max():
            price_highs.append((data.index[i], data['High'].iloc[i], rsi.iloc[i]))

        # Check for price lows
        if data['Low'].iloc[i] == data['Low'].iloc[i-window:i+window+1].min():
            price_lows.append((data.index[i], data['Low'].iloc[i], rsi.iloc[i]))

        # Check for RSI highs
        if rsi.iloc[i] == rsi.iloc[i-window:i+window+1].max() and rsi.iloc[i] > 70:
            rsi_highs.append((data.index[i], data['Close'].iloc[i], rsi.iloc[i]))

        # Check for RSI lows
        if rsi.iloc[i] == rsi.iloc[i-window:i+window+1].min() and rsi.iloc[i] < 30:
            rsi_lows.append((data.index[i], data['Close'].iloc[i], rsi.iloc[i]))

    # Find bearish divergences (price makes higher high but RSI makes lower high)
    bearish_divergences = []

    for i in range(1, len(price_highs)):
        current_date = price_highs[i][0]
        prev_date = price_highs[i-1][0]
        current_price = price_highs[i][1]
        prev_price = price_highs[i-1][1]
        current_rsi = price_highs[i][2]
        prev_rsi = price_highs[i-1][2]

        # If price made higher high but RSI made lower high
        if current_price > prev_price and current_rsi < prev_rsi and current_rsi > 60:
            bearish_divergences.append((prev_date, prev_price, current_date, current_price))

    # Find bullish divergences (price makes lower low but RSI makes higher low)
    bullish_divergences = []

    for i in range(1, len(price_lows)):
        current_date = price_lows[i][0]
        prev_date = price_lows[i-1][0]
        current_price = price_lows[i][1]
        prev_price = price_lows[i-1][1]
        current_rsi = price_lows[i][2]
        prev_rsi = price_lows[i-1][2]

        # If price made lower low but RSI made higher low
        if current_price < prev_price and current_rsi > prev_rsi and current_rsi < 40:
            bullish_divergences.append((prev_date, prev_price, current_date, current_price))

    # Add bearish divergences to chart
    for i, (prev_date, prev_price, current_date, current_price) in enumerate(bearish_divergences):
        # Add a line connecting the divergence points
        fig.add_trace(go.Scatter(
            x=[prev_date, current_date],
            y=[prev_price, current_price],
            mode='lines',
            line=dict(color='red', width=2, dash='dot'),
            name='Bearish Divergence',
            showlegend=(i==0)  # Only show in legend for the first one
        ), row=1, col=1)

    # Add bullish divergences to chart
    for i, (prev_date, prev_price, current_date, current_price) in enumerate(bullish_divergences):
        # Add a line connecting the divergence points
        fig.add_trace(go.Scatter(
            x=[prev_date, current_date],
            y=[prev_price, current_price],
            mode='lines',
            line=dict(color='green', width=2, dash='dot'),
            name='Bullish Divergence',
            showlegend=(i==0)  # Only show in legend for the first one
        ), row=1, col=1)

    # Update layout
    fig.update_layout(
        title=f"{ticker} - RSI Analysis",
        template="plotly_white",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.10,  # Increased to prevent overlap with plot
            xanchor="center",
            x=0.5,  # Centered for better visibility
            font=dict(size=9),  # Smaller font for legend
            itemsizing='constant',  # Consistent legend item sizes
            tracegroupgap=5,  # Reduce gap between legend groups
            bgcolor="rgba(255, 255, 255, 0.9)"  # Semi-transparent background
        ),
        margin=dict(l=40, r=40, t=80, b=60),  # Increased top margin for legend
        hovermode="x unified",
        height=700  # Ensure consistent height
    )

    # Update y-axis for RSI
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="RSI", range=[0, 100], row=2, col=1)

    # Add surge and drawdown periods if available
    if surge_info is not None and not data.empty:
        # Get the surge period dates from surge_info
        surge_start_date_str = surge_info.get('surge_start_date') or surge_info.get('Surge Start Date')
        surge_end_date_str = surge_info.get('surge_end_date') or surge_info.get('Surge End Date')
        drawdown_peak_date_str = surge_info.get('drawdown_peak_date') or surge_info.get('Drawdown Peak Date')
        drawdown_trough_date_str = surge_info.get('drawdown_trough_date') or surge_info.get('Drawdown Trough Date')

        # Convert dates to datetime objects if they exist
        if surge_start_date_str and surge_end_date_str:
            start_date = pd.to_datetime(surge_start_date_str)
            end_date = pd.to_datetime(surge_end_date_str)

            # Add the surge period rectangle
            fig.add_vrect(
                x0=start_date,
                x1=end_date,
                fillcolor="rgba(0, 200, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(0, 150, 0, 0.5)",
                row=1, col=1
            )

        # Add drawdown period if available
        if drawdown_peak_date_str and drawdown_trough_date_str:
            peak_date = pd.to_datetime(drawdown_peak_date_str)
            trough_date = pd.to_datetime(drawdown_trough_date_str)

            # Add the drawdown rectangle
            fig.add_vrect(
                x0=peak_date,
                x1=trough_date,
                fillcolor="rgba(255, 0, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(200, 0, 0, 0.5)",
                row=1, col=1
            )

    # Configure the range slider to appear at the bottom with see-through style
    fig.update_layout(
        xaxis2=dict(
            rangeslider=dict(
                visible=True,
                thickness=0.05,  # Thinner slider
                bgcolor="rgba(211, 211, 211, 0.2)",  # Semi-transparent grey
                bordercolor="rgba(0, 0, 0, 0.1)",    # Subtle border
                borderwidth=1,
                yaxis=dict(rangemode="fixed")  # Fix the y-axis range to prevent overlap
            ),
            type="date",
            automargin=True,  # Ensure date labels are fully visible
            tickformat="%b %Y",  # Format dates as 'Jan 2023'
            tickangle=0,  # Straight date labels
            nticks=10,  # Limit number of ticks for cleaner appearance
            showticklabels=True  # Show tick labels on lower chart
        ),
        xaxis=dict(
            rangeslider=dict(visible=False),  # Disable rangeslider for first axis
            tickangle=0,  # Straight date labels
            showticklabels=False  # Hide tick labels on upper chart
        )
    )

    return fig


def create_volume_chart(ticker, data=None, surge_info=None):
    """Create a chart showing volume analysis for a stock.

    Args:
        ticker (str): The stock ticker symbol
        data (pd.DataFrame, optional): Stock price data. If None, data will be loaded.

    Returns:
        plotly.graph_objects.Figure: A figure showing volume analysis
    """
    # Load data if not provided
    if data is None:
        # Load the stock data
        try:
            data_path = os.path.join('data', 'stocks', f"{ticker}.csv")
            data = pd.read_csv(data_path, index_col='Date', parse_dates=True)
        except FileNotFoundError:
            # Return empty figure if data not found
            return go.Figure().update_layout(
                title=f"No data available for {ticker}",
                xaxis_title="Date",
                yaxis_title="Price",
                template="plotly_white"
            )

    # Make sure we have enough data
    if len(data) < 30:  # Need at least 30 days for meaningful analysis
        return go.Figure().update_layout(
            title=f"Insufficient data for {ticker} volume analysis",
            xaxis_title="Date",
            yaxis_title="Price",
            template="plotly_white",
            annotations=[{
                'text': "Need at least 30 days of data for volume analysis",
                'showarrow': False,
                'font': {'size': 16},
                'xref': 'paper',
                'yref': 'paper',
                'x': 0.5,
                'y': 0.5
            }]
        )

    # Create a single plot with volume as a subplot with 50-50 distribution
    fig = make_subplots(rows=2, cols=1, shared_xaxes=True,
                       vertical_spacing=0.1,  # Increase spacing between subplots
                       row_heights=[0.5, 0.5])  # Equal 50-50 distribution

    # Add candlestick chart for price
    fig.add_trace(go.Candlestick(
        x=data.index,
        open=data['Open'],
        high=data['High'],
        low=data['Low'],
        close=data['Close'],
        name='Price',
        showlegend=True
    ), row=1, col=1)

    # Calculate price change for coloring volume bars
    data['Price_Change'] = data['Close'] - data['Open']

    # Create two separate traces for buy and sell volume to have half-green, half-red legend
    buy_volume = data.copy()
    sell_volume = data.copy()

    # Set volume to NaN where it doesn't match the condition
    buy_volume.loc[data['Price_Change'] < 0, 'Volume'] = np.nan
    sell_volume.loc[data['Price_Change'] >= 0, 'Volume'] = np.nan

    # Add buy volume (green)
    fig.add_trace(go.Bar(
        x=buy_volume.index,
        y=buy_volume['Volume'],
        name='Buy Volume',
        marker=dict(
            color='rgba(0, 180, 0, 0.3)',
            line=dict(color='rgba(0, 0, 0, 0.1)', width=0.5)
        ),
        opacity=0.7
    ), row=2, col=1)

    # Add sell volume (red)
    fig.add_trace(go.Bar(
        x=sell_volume.index,
        y=sell_volume['Volume'],
        name='Sell Volume',
        marker=dict(
            color='rgba(180, 0, 0, 0.3)',
            line=dict(color='rgba(0, 0, 0, 0.1)', width=0.5)
        ),
        opacity=0.7
    ), row=2, col=1)

    # Calculate volume moving average (20-day)
    volume_ma = data['Volume'].rolling(window=20).mean()

    fig.add_trace(go.Scatter(
        x=data.index,
        y=volume_ma,
        mode='lines',
        name='Volume MA (20)',
        line=dict(color='blue', width=1.5)
    ), row=2, col=1)

    # Detect volume anomalies (spikes)
    # Calculate volume ratio compared to moving average
    volume_ratio = data['Volume'] / volume_ma
    # Find days where volume is more than 2x the average
    volume_spikes = volume_ratio[volume_ratio > 2]

    # Add all volume spikes in a single trace
    if not volume_spikes.empty:
        spike_dates = volume_spikes.index
        spike_values = [data.loc[date, 'Volume'] for date in spike_dates]

        fig.add_trace(go.Scatter(
            x=spike_dates,
            y=spike_values,
            mode='markers',
            marker=dict(size=10, color='purple', symbol='circle'),
            name='Volume Spike',
            showlegend=True
        ), row=2, col=1)

    # Add price-volume correlation indicators
    # Calculate volume-weighted price change
    data['Vol_Weighted_Change'] = data['Price_Change'] * data['Volume'] / data['Volume'].mean()

    # Add annotations for significant volume-price events
    significant_events = []

    # Find days with high volume and significant price change
    high_vol_price_change = data[(volume_ratio > 1.5) & (abs(data['Price_Change']/data['Open']) > 0.02)]

    if not high_vol_price_change.empty:
        for date, row in high_vol_price_change.iterrows():
            direction = "up" if row['Price_Change'] > 0 else "down"
            significant_events.append((date, row['Close'], direction))

    # Add markers for significant volume-price events
    if significant_events:
        up_events = [(d, p) for d, p, dir in significant_events if dir == "up"]
        down_events = [(d, p) for d, p, dir in significant_events if dir == "down"]

        if up_events:
            fig.add_trace(go.Scatter(
                x=[e[0] for e in up_events],
                y=[e[1] for e in up_events],
                mode='markers',
                marker=dict(size=8, color='green', symbol='triangle-up'),
                name='High Vol + Price Up',
                showlegend=True
            ), row=1, col=1)

        if down_events:
            fig.add_trace(go.Scatter(
                x=[e[0] for e in down_events],
                y=[e[1] for e in down_events],
                mode='markers',
                marker=dict(size=8, color='red', symbol='triangle-down'),
                name='High Vol + Price Down',
                showlegend=True
            ), row=1, col=1)

    # Update layout
    fig.update_layout(
        title=f"{ticker} - Volume Analysis",
        template="plotly_white",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.10,  # Increased to prevent overlap with plot
            xanchor="center",
            x=0.5,  # Centered for better visibility
            font=dict(size=9),  # Smaller font for legend
            itemsizing='constant',  # Consistent legend item sizes
            tracegroupgap=5,  # Reduce gap between legend groups
            bgcolor="rgba(255, 255, 255, 0.9)"  # Semi-transparent background
        ),
        margin=dict(l=40, r=40, t=80, b=40),  # Increased top margin for legend
        hovermode="x unified",
        height=700  # Ensure consistent height
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price ($)", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)

    # Add surge and drawdown periods if available
    if surge_info is not None and not data.empty:
        # Get the surge period dates from surge_info
        surge_start_date_str = surge_info.get('surge_start_date') or surge_info.get('Surge Start Date')
        surge_end_date_str = surge_info.get('surge_end_date') or surge_info.get('Surge End Date')
        drawdown_peak_date_str = surge_info.get('drawdown_peak_date') or surge_info.get('Drawdown Peak Date')
        drawdown_trough_date_str = surge_info.get('drawdown_trough_date') or surge_info.get('Drawdown Trough Date')

        # Convert dates to datetime objects if they exist
        if surge_start_date_str and surge_end_date_str:
            start_date = pd.to_datetime(surge_start_date_str)
            end_date = pd.to_datetime(surge_end_date_str)

            # Add the surge period rectangle
            fig.add_vrect(
                x0=start_date,
                x1=end_date,
                fillcolor="rgba(0, 200, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(0, 150, 0, 0.5)",
                row=1, col=1
            )

        # Add drawdown period if available
        if drawdown_peak_date_str and drawdown_trough_date_str:
            peak_date = pd.to_datetime(drawdown_peak_date_str)
            trough_date = pd.to_datetime(drawdown_trough_date_str)

            # Add the drawdown rectangle
            fig.add_vrect(
                x0=peak_date,
                x1=trough_date,
                fillcolor="rgba(255, 0, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(200, 0, 0, 0.5)",
                row=1, col=1
            )

    # No need for empty trace since we're using the main x-axis for the range slider

    # Configure the range slider to appear at the bottom with see-through style
    fig.update_layout(
        xaxis2=dict(
            rangeslider=dict(
                visible=True,
                thickness=0.05,  # Thinner slider
                bgcolor="rgba(211, 211, 211, 0.2)",  # Semi-transparent grey
                bordercolor="rgba(0, 0, 0, 0.1)",    # Subtle border
                borderwidth=1,
                yaxis=dict(rangemode="fixed")  # Fix the y-axis range to prevent overlap
            ),
            type="date",
            automargin=True,  # Ensure date labels are fully visible
            tickformat="%b %Y",  # Format dates as 'Jan 2023'
            tickangle=0,  # Straight date labels
            nticks=10,  # Limit number of ticks for cleaner appearance
            showticklabels=True  # Show tick labels on lower chart
        ),
        xaxis=dict(
            rangeslider=dict(visible=False),  # Disable rangeslider for first axis
            tickangle=0,  # Straight date labels
            type="date",  # Explicitly set as date type
            automargin=True,  # Ensure date labels are fully visible
            tickformat="%b %Y",  # Format dates as 'Jan 2023'
            nticks=10,  # Limit number of ticks for cleaner appearance
            showticklabels=False  # Hide tick labels on upper chart
        )
    )

    return fig


def create_support_resistance_chart(ticker, data=None, surge_info=None):
    """Create a chart showing support and resistance levels for a stock.

    Args:
        ticker (str): The stock ticker symbol
        data (pd.DataFrame, optional): Stock price data. If None, data will be loaded.

    Returns:
        plotly.graph_objects.Figure: A figure showing support and resistance levels
    """
    # Load data if not provided
    if data is None:
        # Load the stock data
        try:
            data_path = os.path.join('data', 'stocks', f"{ticker}.csv")
            data = pd.read_csv(data_path, index_col='Date', parse_dates=True)
        except FileNotFoundError:
            # Return empty figure if data not found
            return go.Figure().update_layout(
                title=f"No data available for {ticker}",
                xaxis_title="Date",
                yaxis_title="Price",
                template="plotly_white"
            )

    # Make sure we have enough data
    if len(data) < 60:  # Need at least 60 days for meaningful analysis
        return go.Figure().update_layout(
            title=f"Insufficient data for {ticker} support/resistance analysis",
            xaxis_title="Date",
            yaxis_title="Price",
            template="plotly_white",
            annotations=[{
                'text': "Need at least 60 days of data for support/resistance analysis",
                'showarrow': False,
                'font': {'size': 16},
                'xref': 'paper',
                'yref': 'paper',
                'x': 0.5,
                'y': 0.5
            }]
        )

    # Create the figure with subplots for better organization
    fig = make_subplots(rows=1, cols=1, shared_xaxes=True,
                       specs=[[{"secondary_y": True}]],
                       vertical_spacing=0.05)  # Single row with secondary y-axis for volume

    # Add candlestick chart
    fig.add_trace(go.Candlestick(
        x=data.index,
        open=data['Open'],
        high=data['High'],
        low=data['Low'],
        close=data['Close'],
        name='Price',
        showlegend=True
    ), row=1, col=1)

    # Find support and resistance levels using pivot points
    # Calculate pivot points
    pivot_high = []
    pivot_low = []

    # Use a smaller window for more recent data to ensure we have levels in 1m view
    window = min(10, len(data) // 10)  # Adaptive window size based on data length

    for i in range(window, len(data) - window):
        # Check if this is a local maximum
        if data['High'].iloc[i] == data['High'].iloc[i-window:i+window+1].max():
            pivot_high.append((data.index[i], data['High'].iloc[i]))

        # Check if this is a local minimum
        if data['Low'].iloc[i] == data['Low'].iloc[i-window:i+window+1].min():
            pivot_low.append((data.index[i], data['Low'].iloc[i]))

    # Group nearby pivot points to find support/resistance zones
    support_levels = []
    resistance_levels = []

    # Function to group nearby price levels
    def group_levels(pivots, threshold_percent=0.03):  # Increased threshold to group more levels
        if not pivots:
            return []

        # Sort by price
        sorted_pivots = sorted(pivots, key=lambda x: x[1])

        # Group nearby levels
        groups = []
        current_group = [sorted_pivots[0]]

        for i in range(1, len(sorted_pivots)):
            current_price = sorted_pivots[i][1]
            group_avg_price = sum(p[1] for p in current_group) / len(current_group)

            # If this price is within threshold of the group average, add to group
            if abs(current_price - group_avg_price) / group_avg_price < threshold_percent:
                current_group.append(sorted_pivots[i])
            else:
                # Start a new group
                groups.append(current_group)
                current_group = [sorted_pivots[i]]

        # Add the last group
        if current_group:
            groups.append(current_group)

        # Calculate average price for each group
        return [(group[0][0], sum(p[1] for p in group) / len(group)) for group in groups]

    # Group the pivot points
    support_levels = group_levels(pivot_low)
    resistance_levels = group_levels(pivot_high)

    # Limit the number of levels to show (most significant ones)
    # Sort by frequency of touches or strength
    def get_top_levels(levels, max_count=4):  # Reduced to 4 for less clutter
        if len(levels) <= max_count:
            return levels

        # For simplicity, just take evenly spaced levels across the range
        indices = np.linspace(0, len(levels)-1, max_count, dtype=int)
        return [levels[i] for i in indices]

    support_levels = get_top_levels(support_levels)
    resistance_levels = get_top_levels(resistance_levels)

    # Sort support levels by price (ascending)
    support_levels = sorted(support_levels, key=lambda x: x[1])

    # Create a list to track all label positions to prevent overlaps between support and resistance labels
    label_positions = []

    # Add support levels to row 1
    for i, (_, level) in enumerate(support_levels):
        fig.add_shape(
            type="line",
            x0=data.index[0],
            y0=level,
            x1=data.index[-1],
            y1=level,
            line=dict(color="green", width=2, dash="dash"),
            row=1, col=1
        )

        # Find all levels that are close to this one
        close_levels = []
        for _, other_level in support_levels:
            if level != other_level and abs(level - other_level) / level < 0.05:  # If levels are within 5%
                close_levels.append(other_level)

        # Determine if we need arrows based on proximity to other levels
        use_arrow = len(close_levels) > 0

        # Position annotation at the end of the line
        y_pos = level

        # Calculate horizontal position - place all labels very far to the right
        # to avoid overlapping with the volume axis labels
        x_pos = data.index[-1]  # Position at the end of the data
        x_shift = 100  # Shift very far to the right to avoid volume axis labels

        # Simply add this level to the tracked positions without any offset calculations
        label_positions.append(level)

        # Set vertical offset to 0 since we're removing positional biases
        vertical_offset = 0

        # Position annotation at the far right of the chart
        # This ensures it doesn't overlap with the volume axis
        x_pos = data.index[-1]  # Position at the end of the data

        fig.add_annotation(
            x=x_pos,  # Position at the end of the data
            y=y_pos,
            text=f"S: {level:.2f}",  # Shortened label
            showarrow=use_arrow,  # Use arrow only when levels are close
            arrowhead=2,
            arrowsize=1,
            arrowwidth=1,
            arrowcolor="green",
            font=dict(size=10, color='green'),  # Green text
            bgcolor="rgba(255, 255, 255, 0.8)",  # White background with transparency
            bordercolor="green",
            borderwidth=1,
            borderpad=2,
            xanchor="right",  # Anchor to the right of the text
            yanchor="middle",  # Center vertically
            xshift=75,  # Consistent shift to the right for all labels
            yshift=vertical_offset  # Apply vertical offset to prevent overlaps
        )

    # Sort resistance levels by price (descending)
    resistance_levels = sorted(resistance_levels, key=lambda x: x[1], reverse=True)

    # Add resistance levels to row 1
    for i, (_, level) in enumerate(resistance_levels):
        fig.add_shape(
            type="line",
            x0=data.index[0],
            y0=level,
            x1=data.index[-1],
            y1=level,
            line=dict(color="red", width=2, dash="dash"),
            row=1, col=1
        )

        # Find all levels that are close to this one
        close_levels = []
        for _, other_level in resistance_levels:
            if level != other_level and abs(level - other_level) / level < 0.05:  # If levels are within 5%
                close_levels.append(other_level)

        # Determine if we need arrows based on proximity to other levels
        use_arrow = len(close_levels) > 0

        # Position annotation at the end of the line
        y_pos = level

        # Calculate horizontal position - place all labels very far to the right
        # to avoid overlapping with the volume axis labels
        x_pos = data.index[-1]  # Position at the end of the data
        x_shift = 100  # Shift very far to the right to avoid volume axis labels

        # Simply add this level to the tracked positions without any offset calculations
        label_positions.append(level)

        # Set vertical offset to 0 since we're removing positional biases
        vertical_offset = 0

        # Position annotation at the far right of the chart
        # This ensures it doesn't overlap with the volume axis
        x_pos = data.index[-1]  # Position at the end of the data

        fig.add_annotation(
            x=x_pos,  # Position at the end of the data
            y=y_pos,
            text=f"R: {level:.2f}",  # Shortened label
            showarrow=use_arrow,  # Use arrow only when levels are close
            arrowhead=2,
            arrowsize=1,
            arrowwidth=1,
            arrowcolor="red",
            font=dict(size=10, color='red'),  # Red text
            bgcolor="rgba(255, 255, 255, 0.8)",  # White background with transparency
            bordercolor="red",
            borderwidth=1,
            borderpad=2,
            xanchor="right",  # Anchor to the right of the text
            yanchor="middle",  # Center vertically
            xshift=130,  # Shift resistance labels further to the right
            yshift=vertical_offset  # Apply vertical offset to prevent overlaps
        )

    # Add breakout points (when price crosses a support/resistance level)
    breakouts = []

    # Check for breakouts
    for i in range(1, len(data)):
        current_close = data['Close'].iloc[i]
        prev_close = data['Close'].iloc[i-1]

        # Check if price crossed any resistance level (bullish breakout)
        for _, level in resistance_levels:
            if prev_close < level and current_close > level:
                breakouts.append((data.index[i], current_close, 'bullish'))

        # Check if price crossed any support level (bearish breakdown)
        for _, level in support_levels:
            if prev_close > level and current_close < level:
                breakouts.append((data.index[i], current_close, 'bearish'))

    # Group breakouts by type
    bullish_breakouts = []
    bearish_breakouts = []

    for date, price, breakout_type in breakouts:
        if breakout_type == 'bullish':
            bullish_breakouts.append((date, price))
        else:  # bearish
            bearish_breakouts.append((date, price))

    # Add bullish breakouts with optimized positioning
    if bullish_breakouts:
        # Add a small offset to the y-position to prevent overlapping
        bullish_y = [b[1] * 1.01 for b in bullish_breakouts]  # Position slightly above the price

        fig.add_trace(go.Scatter(
            x=[b[0] for b in bullish_breakouts],
            y=bullish_y,
            mode='markers',
            marker=dict(size=10, color='green', symbol='triangle-up'),
            name='Bullish Breakout',
            showlegend=True
        ), row=1, col=1)

    # Add bearish breakouts with optimized positioning
    if bearish_breakouts:
        # Add a small offset to the y-position to prevent overlapping
        bearish_y = [b[1] * 0.99 for b in bearish_breakouts]  # Position slightly below the price

        fig.add_trace(go.Scatter(
            x=[b[0] for b in bearish_breakouts],
            y=bearish_y,
            mode='markers',
            marker=dict(size=10, color='red', symbol='triangle-down'),
            name='Bearish Breakout',
            showlegend=True
        ), row=1, col=1)

    # Calculate price change for coloring volume bars
    data['Price_Change'] = data['Close'] - data['Open']

    # Add volume as a secondary y-axis on the same plot

    # Create two separate traces for buy and sell volume to have half-green, half-red legend
    buy_volume = data.copy()
    sell_volume = data.copy()

    # Set volume to NaN where it doesn't match the condition
    buy_volume.loc[data['Price_Change'] < 0, 'Volume'] = np.nan
    sell_volume.loc[data['Price_Change'] >= 0, 'Volume'] = np.nan

    # Add buy volume (green) with even lower opacity to prevent affecting range slider appearance
    fig.add_trace(go.Bar(
        x=buy_volume.index,
        y=buy_volume['Volume'],
        name='Buy Volume',
        marker=dict(
            color='rgba(0, 180, 0, 0.3)',  # Further reduced opacity
            line=dict(color='rgba(0, 0, 0, 0.1)', width=0.5)
        ),
        opacity=0.4  # Further reduced opacity
    ), row=1, col=1, secondary_y=True)

    # Add sell volume (red) with even lower opacity to prevent affecting range slider appearance
    fig.add_trace(go.Bar(
        x=sell_volume.index,
        y=sell_volume['Volume'],
        name='Sell Volume',
        marker=dict(
            color='rgba(180, 0, 0, 0.3)',  # Further reduced opacity
            line=dict(color='rgba(0, 0, 0, 0.1)', width=0.5)
        ),
        opacity=0.4  # Further reduced opacity
    ), row=1, col=1, secondary_y=True)

    # Update layout
    fig.update_layout(
        title=f"{ticker} - Support and Resistance Analysis",
        # Removed xaxis_title="Date" to eliminate the label
        yaxis_title="Price ($)",
        template="plotly_white",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.10,  # Increased to prevent overlap with plot
            xanchor="center",
            x=0.5,  # Centered for better visibility
            font=dict(size=9),  # Smaller font for legend
            itemsizing='constant',  # Consistent legend item sizes
            tracegroupgap=5,  # Reduce gap between legend groups
            bgcolor="rgba(255, 255, 255, 0.9)"  # Semi-transparent background
        ),
        margin=dict(l=40, r=80, t=80, b=60),  # Further reduced right margin to remove unnecessary white space
        hovermode="x unified",
        height=700  # Ensure consistent height
    )

    # Update y-axis labels
    fig.update_yaxes(title_text="Price ($)", row=1, col=1, secondary_y=False)
    fig.update_yaxes(
        title_text="Volume",
        row=1,
        col=1,
        secondary_y=True,
        showticklabels=True,  # Show volume tick labels
        side="right",  # Ensure volume axis is on the right
        position=0.95  # Position volume axis further to the right to reduce white space
    )

    # Add surge and drawdown periods if available
    if surge_info is not None and not data.empty:
        # Get the surge period dates from surge_info
        surge_start_date_str = surge_info.get('surge_start_date') or surge_info.get('Surge Start Date')
        surge_end_date_str = surge_info.get('surge_end_date') or surge_info.get('Surge End Date')
        drawdown_peak_date_str = surge_info.get('drawdown_peak_date') or surge_info.get('Drawdown Peak Date')
        drawdown_trough_date_str = surge_info.get('drawdown_trough_date') or surge_info.get('Drawdown Trough Date')

        # Convert dates to datetime objects if they exist
        if surge_start_date_str and surge_end_date_str:
            start_date = pd.to_datetime(surge_start_date_str)
            end_date = pd.to_datetime(surge_end_date_str)

            # Add the surge period rectangle
            fig.add_vrect(
                x0=start_date,
                x1=end_date,
                fillcolor="rgba(0, 200, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(0, 150, 0, 0.5)",
                row=1, col=1
            )

        # Add drawdown period if available
        if drawdown_peak_date_str and drawdown_trough_date_str:
            peak_date = pd.to_datetime(drawdown_peak_date_str)
            trough_date = pd.to_datetime(drawdown_trough_date_str)

            # Add the drawdown rectangle
            fig.add_vrect(
                x0=peak_date,
                x1=trough_date,
                fillcolor="rgba(255, 0, 0, 0.2)",
                opacity=0.7,
                layer="below",
                line_width=1,
                line_color="rgba(200, 0, 0, 0.5)",
                row=1, col=1
            )

    # Configure the range slider to appear at the bottom with see-through style
    # Use the same configuration as other tabs for consistency
    fig.update_layout(
        xaxis=dict(
            rangeslider=dict(
                visible=True,
                thickness=0.05,  # Thinner slider
                bgcolor="rgba(211, 211, 211, 0.2)",  # Semi-transparent grey
                bordercolor="rgba(0, 0, 0, 0.1)",    # Subtle border
                borderwidth=1,
                yaxis=dict(rangemode="fixed")  # Fix the y-axis range to prevent overlap
            ),
            type="date",
            automargin=True,  # Ensure date labels are fully visible
            tickformat="%b %Y",  # Format dates as 'Jan 2023'
            tickangle=0,  # Straight date labels
            nticks=10,  # Limit number of ticks for cleaner appearance
            showticklabels=True  # Show tick labels since this is a single plot
        )
    )

    # Ensure the range slider is properly layered
    fig.update_layout(hovermode="x unified")

    return fig


# Root cause analysis function has been removed as requested


def create_price_chart(ticker, start_date=None, end_date=None, surge_info=None):
    """
    Create a price chart for a stock.

    Args:
        ticker: Stock ticker symbol
        start_date: Start date for the chart (optional)
        end_date: End date for the chart (optional)
        surge_info: Dictionary containing surge information (optional)

    Returns:
        Plotly figure object
    """
    print(f"\n=== Creating price chart for {ticker} ===")
    if surge_info is not None:
        print(f"Surge info available: {list(surge_info.keys())}")

    try:
        # Create a dictionary to track all annotations and their positions
        # This will help us prevent overlaps
        annotation_positions = []

        # Function to find a non-overlapping position for an annotation
        def find_non_overlapping_position(x, y, width=0.05, height=0.03, x_shift_options=None, y_shift_options=None):
            """Find a position for an annotation that doesn't overlap with existing ones."""
            # Default shift options if none provided
            if x_shift_options is None:
                x_shift_options = [0, 0.02, -0.02, 0.04, -0.04, 0.06, -0.06]
            if y_shift_options is None:
                y_shift_options = [0, 0.03, -0.03, 0.06, -0.06, 0.09, -0.09]

            # Try different positions until we find one that doesn't overlap
            for x_shift in x_shift_options:
                for y_shift in y_shift_options:
                    new_x = x + x_shift
                    new_y = y + y_shift

                    # Check if this position overlaps with any existing annotation
                    overlap = False
                    for pos in annotation_positions:
                        if (abs(new_x - pos['x']) < (width + pos['width'])/2 and
                            abs(new_y - pos['y']) < (height + pos['height'])/2):
                            overlap = True
                            break

                    if not overlap:
                        # Add this position to our tracking list
                        annotation_positions.append({
                            'x': new_x, 'y': new_y, 'width': width, 'height': height
                        })
                        return new_x, new_y

            # If all positions overlap, just return the original with a random offset
            # This is a fallback to ensure we always return something
            import random
            new_x = x + random.uniform(-0.1, 0.1)
            new_y = y + random.uniform(-0.1, 0.1)
            annotation_positions.append({
                'x': new_x, 'y': new_y, 'width': width, 'height': height
            })
            return new_x, new_y

        # Fetch stock data directly using yfinance
        data = fetch_stock_data(ticker)
        print(f"Received data with {len(data)} rows")

        # Print data information for debugging
        if not data.empty:
            print(f"Data shape: {data.shape}")
            print(f"Data columns: {data.columns}")
            print(f"Data index range: {data.index[0]} to {data.index[-1]}")
            print(f"Sample data:\n{data.head(3)}")

        if data.empty:
            # Return an empty figure with a message if no data
            print(f"No data available for {ticker}, returning empty chart")
            fig = go.Figure()
            fig.update_layout(
                title=f"No data available for {ticker}",
                annotations=[{
                    'text': f"No historical data found for {ticker}.",
                    'showarrow': False,
                    'font': {'size': 16},
                    'x': 0.5,
                    'y': 0.5,
                    'xref': 'paper',
                    'yref': 'paper',
                }]
            )
            return fig

        # Create figure with subplots
        fig = go.Figure()

        # Add price line with gradient fill
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['Close'],
            mode='lines',
            name='Close Price',
            line=dict(color='#2c7fb8', width=2),
            fill='tozeroy',
            fillcolor='rgba(44, 127, 184, 0.1)'
        ))

        # Calculate whether each day was up or down
        data['Price_Change'] = data['Close'].diff()

        # Create two separate traces for buy and sell volume to have half-green, half-red legend
        buy_volume = data.copy()
        sell_volume = data.copy()

        # Set volume to NaN where it doesn't match the condition
        buy_volume.loc[data['Price_Change'] < 0, 'Volume'] = np.nan
        sell_volume.loc[data['Price_Change'] >= 0, 'Volume'] = np.nan

        # Add buy volume (green)
        fig.add_trace(go.Bar(
            x=buy_volume.index,
            y=buy_volume['Volume'],
            name='Buy Volume',
            yaxis='y2',
            marker=dict(
                color='rgba(0, 180, 0, 0.4)',
                line=dict(color='rgba(0, 0, 0, 0.1)', width=0.5)
            ),
            opacity=0.7
        ))

        # Add sell volume (red)
        fig.add_trace(go.Bar(
            x=sell_volume.index,
            y=sell_volume['Volume'],
            name='Sell Volume',
            yaxis='y2',
            marker=dict(
                color='rgba(180, 0, 0, 0.4)',
                line=dict(color='rgba(0, 0, 0, 0.1)', width=0.5)
            ),
            opacity=0.7
        ))

        # Add moving averages for better visualization
        # 50-day moving average
        data['MA50'] = data['Close'].rolling(window=50).mean()
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['MA50'],
            mode='lines',
            name='50-Day MA',
            line=dict(color='orange', width=1.5)
        ))

        # 200-day moving average
        data['MA200'] = data['Close'].rolling(window=200).mean()
        fig.add_trace(go.Scatter(
            x=data.index,
            y=data['MA200'],
            mode='lines',
            name='200-Day MA',
            line=dict(color='red', width=1.5)
        ))

        # Add surge information if available
        if surge_info is not None and not data.empty:
            print(f"Adding surge analysis visualization")

            # Get the surge period dates from surge_info
            print(f"DEBUG: All surge_info keys: {surge_info.keys()}")

            # First try to get the specific surge period dates (new format)
            surge_start_date_str = surge_info.get('surge_start_date') or surge_info.get('Surge Start Date')
            surge_end_date_str = surge_info.get('surge_end_date') or surge_info.get('Surge End Date')
            surge_start_price = surge_info.get('surge_start_price') or surge_info.get('Surge Start Price')
            surge_end_price = surge_info.get('surge_end_price') or surge_info.get('Surge End Price')
            surge_percent_change = surge_info.get('surge_percent_change') or surge_info.get('Surge Percent Change')

            print(f"DEBUG: Specific surge period from data:")
            print(f"DEBUG: surge_start_date_str = {surge_start_date_str}")
            print(f"DEBUG: surge_end_date_str = {surge_end_date_str}")
            print(f"DEBUG: surge_start_price = {surge_start_price}")
            print(f"DEBUG: surge_end_price = {surge_end_price}")
            print(f"DEBUG: surge_percent_change = {surge_percent_change}")

            # If specific surge period not available, fall back to full period (old format)
            if not surge_start_date_str or not surge_end_date_str:
                print("Specific surge period not found, falling back to full period")
                surge_start_date_str = surge_info.get('start_date') or surge_info.get('Start Date')
                surge_end_date_str = surge_info.get('end_date') or surge_info.get('End Date')
                surge_start_price = surge_info.get('start_price') or surge_info.get('Start Price')
                surge_end_price = surge_info.get('end_price') or surge_info.get('End Price')
                surge_percent_change = surge_info.get('total_change_percent') or surge_info.get('Total Change %')

            print(f"Surge info keys: {surge_info.keys()}")
            print(f"Surge start date: {surge_start_date_str}")
            print(f"Surge end date: {surge_end_date_str}")
            print(f"Surge start price: {surge_start_price}")
            print(f"Surge end price: {surge_end_price}")
            print(f"Surge percent change: {surge_percent_change}")

            # Convert dates to datetime objects
            if surge_start_date_str and surge_end_date_str:
                try:
                    # Convert dates to datetime objects and ensure they're timezone-aware
                    start_date = pd.to_datetime(surge_start_date_str)
                    end_date = pd.to_datetime(surge_end_date_str)

                    # Make the dates timezone-aware if the data index is timezone-aware
                    if data.index.tz is not None:
                        if start_date.tz is None:
                            start_date = start_date.tz_localize(data.index.tz)
                        if end_date.tz is None:
                            end_date = end_date.tz_localize(data.index.tz)
                    else:
                        # If data index is timezone-naive, make the dates timezone-naive too
                        if start_date.tz is not None:
                            start_date = start_date.tz_localize(None)
                        if end_date.tz is not None:
                            end_date = end_date.tz_localize(None)

                    print(f"Actual surge period from data: {start_date} to {end_date}")

                    # Check if the dates are in the correct order (start should be before end for visualization)
                    # If they're reversed, swap them for visualization purposes
                    dates_reversed = False
                    if start_date > end_date:
                        print(f"DEBUG: Surge dates are reversed: start {start_date} is after end {end_date}")
                        # Swap the dates for visualization purposes
                        start_date, end_date = end_date, start_date
                        dates_reversed = True
                        print(f"DEBUG: After swapping: start {start_date} to end {end_date}")

                    # Find the closest dates in our data
                    if start_date < data.index.min():
                        start_date = data.index.min()
                    if end_date > data.index.max():
                        end_date = data.index.max()

                    # Find the closest dates in our data
                    start_date = data.index[data.index.get_indexer([start_date], method='nearest')[0]]
                    end_date = data.index[data.index.get_indexer([end_date], method='nearest')[0]]

                    # Get the prices at those dates
                    start_price = float(data.loc[start_date, 'Close'])
                    end_price = float(data.loc[end_date, 'Close'])

                    # Calculate the percentage change
                    percent_change = ((end_price - start_price) / start_price) * 100

                    print(f"Using actual surge period: {start_date} to {end_date}")
                    print(f"Prices: {start_price:.2f} to {end_price:.2f}")
                    print(f"Percentage change: {percent_change:.2f}%")
                except Exception as e:
                    print(f"Error processing surge dates: {e}")
                    # Try to use the original values directly if they're available
                    if isinstance(surge_start_price, (int, float)) and isinstance(surge_end_price, (int, float)):
                        # Find dates closest to the start and end of the data
                        start_date = data.index[0]
                        end_date = data.index[-1]
                        percent_change = surge_percent_change if surge_percent_change else ((surge_end_price - surge_start_price) / surge_start_price) * 100
                        print(f"Using original price values with data range dates: {start_date} to {end_date}")
                        print(f"Original prices: {surge_start_price:.2f} to {surge_end_price:.2f}")
                        print(f"Original percentage change: {percent_change:.2f}%")
                    else:
                        # Fall back to using a portion of the data for visualization
                        data_range = len(data)
                        start_idx = int(data_range * 0.25)
                        end_idx = int(data_range * 0.75)

                        # Make sure we have valid indices
                        start_idx = max(0, min(start_idx, data_range - 1))
                        end_idx = max(0, min(end_idx, data_range - 1))

                        # Get the dates at these positions
                        start_date = data.index[start_idx]
                        end_date = data.index[end_idx]

                        # Get the prices at those dates
                        start_price = float(data.loc[start_date, 'Close'])
                        end_price = float(data.loc[end_date, 'Close'])

                        # Calculate the percentage change
                        percent_change = ((end_price - start_price) / start_price) * 100

                        print(f"Falling back to estimated surge period: {start_date} to {end_date}")
                        print(f"Estimated prices: {start_price:.2f} to {end_price:.2f}")
                        print(f"Estimated percentage change: {percent_change:.2f}%")
            else:
                # If we don't have start and end dates, use a portion of the data for visualization
                data_range = len(data)
                start_idx = int(data_range * 0.25)
                end_idx = int(data_range * 0.75)

                # Make sure we have valid indices
                start_idx = max(0, min(start_idx, data_range - 1))
                end_idx = max(0, min(end_idx, data_range - 1))

                # Get the dates at these positions
                start_date = data.index[start_idx]
                end_date = data.index[end_idx]

                # Get the prices at those dates
                start_price = float(data.loc[start_date, 'Close'])
                end_price = float(data.loc[end_date, 'Close'])

                # Calculate the percentage change
                percent_change = ((end_price - start_price) / start_price) * 100

                print(f"Using estimated surge period: {start_date} to {end_date}")
                print(f"Estimated prices: {start_price:.2f} to {end_price:.2f}")
                print(f"Estimated percentage change: {percent_change:.2f}%")

            # Make sure we have valid start_date, end_date, start_price, and end_price
            try:
                # Verify we have all required variables
                if not all(var is not None for var in [start_date, end_date, start_price, end_price]):
                    print(f"Warning: Missing required variables for surge visualization")
                    print(f"start_date: {start_date}, end_date: {end_date}, start_price: {start_price}, end_price: {end_price}")
                    # Try to recover by using the data range
                    if data is not None and not data.empty:
                        if start_date is None:
                            start_date = data.index[0]
                        if end_date is None:
                            end_date = data.index[-1]
                        if start_price is None and start_date is not None:
                            start_price = float(data.loc[start_date, 'Close'])
                        if end_price is None and end_date is not None:
                            end_price = float(data.loc[end_date, 'Close'])

                # Add surge period highlight if we have valid dates
                if start_date is not None and end_date is not None:
                    # Add the surge period rectangle with built-in annotation
                    fig.add_vrect(
                        x0=start_date,
                        x1=end_date,
                        fillcolor="rgba(0, 200, 0, 0.2)",
                        opacity=0.7,
                        layer="below",
                        line_width=1,
                        line_color="rgba(0, 150, 0, 0.5)",
                        annotation_text="Surge Period",
                        annotation_position="top left",
                        annotation_font=dict(size=12, color="darkgreen", family="Arial Black"),
                        # Ensure the rectangle stays within the plot area
                        yref="paper",
                        y0=0,
                        y1=1
                    )

                    # We're now using the built-in annotation in the vrect

                # Add start point marker if we have valid start date and price
                if start_date is not None and start_price is not None:
                    # Calculate a better position for the text to avoid overlap
                    # Position text below and slightly to the left of the marker
                    fig.add_trace(go.Scatter(
                        x=[start_date],
                        y=[start_price],
                        mode='markers',  # Only marker, we'll add text separately
                        name='Surge Start',
                        marker=dict(color='green', size=10, symbol='circle'),
                        hoverinfo='text',
                        hovertext=f"Surge Start: {start_date.strftime('%Y-%m-%d')}<br>Price: ${start_price:.2f}"
                    ))

                    # Use our anti-overlap function to find a good position for the annotation
                    # Convert date to a normalized position (0-1 range) for our algorithm
                    date_range = (data.index[-1] - data.index[0]).total_seconds()
                    date_pos = (start_date - data.index[0]).total_seconds() / date_range if date_range > 0 else 0.5

                    # Convert price to a normalized position (0-1 range)
                    price_range = data['Close'].max() - data['Close'].min()
                    price_pos = (start_price - data['Close'].min()) / price_range if price_range > 0 else 0.5

                    # For start price, prefer positions to the left and below
                    x_shift_options = [-0.03, -0.05, -0.07, -0.09, 0.03, 0.05]
                    y_shift_options = [-0.05, -0.08, -0.1, -0.15, 0.05, 0.08]

                    # Find a non-overlapping position with larger width/height to ensure more spacing
                    label_x, label_y = find_non_overlapping_position(date_pos, price_pos,
                                                                    width=0.1, height=0.07,
                                                                    x_shift_options=x_shift_options,
                                                                    y_shift_options=y_shift_options)

                    # Convert back to data coordinates
                    label_date = data.index[0] + pd.Timedelta(seconds=label_x * date_range)
                    label_price = data['Close'].min() + (label_y * price_range)

                    # Add the annotation with the optimized position
                    fig.add_annotation(
                        x=label_date,
                        y=label_price,
                        text=f"${start_price:.2f}",
                        showarrow=True,
                        arrowhead=2,
                        arrowsize=1,
                        arrowwidth=1,
                        arrowcolor="green",
                        font=dict(size=10, color='green'),
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="green",
                        borderwidth=1,
                        borderpad=2,
                        xanchor="center",
                        yanchor="middle",
                        ax=20 * (label_x - date_pos),  # Adjust arrow direction based on position
                        ay=20 * (label_y - price_pos)  # Adjust arrow direction based on position
                    )

                # Add end point marker if we have valid end date and price
                if end_date is not None and end_price is not None:
                    # Calculate a better position for the text to avoid overlap
                    # Position text above and slightly to the right of the marker
                    fig.add_trace(go.Scatter(
                        x=[end_date],
                        y=[end_price],
                        mode='markers',  # Only marker, we'll add text separately
                        name='Surge End',
                        marker=dict(color='green', size=10, symbol='circle'),
                        hoverinfo='text',
                        hovertext=f"Surge End: {end_date.strftime('%Y-%m-%d')}<br>Price: ${end_price:.2f}"
                    ))

                    # Use our anti-overlap function to find a good position for the annotation
                    # Convert date to a normalized position (0-1 range) for our algorithm
                    date_range = (data.index[-1] - data.index[0]).total_seconds()
                    date_pos = (end_date - data.index[0]).total_seconds() / date_range if date_range > 0 else 0.5

                    # Convert price to a normalized position (0-1 range)
                    price_range = data['Close'].max() - data['Close'].min()
                    price_pos = (end_price - data['Close'].min()) / price_range if price_range > 0 else 0.5

                    # For end price, prefer positions to the right and above
                    x_shift_options = [0.03, 0.05, 0.07, 0.09, -0.03, -0.05]
                    y_shift_options = [0.05, 0.08, 0.1, 0.15, -0.05, -0.08]

                    # Find a non-overlapping position with larger width/height to ensure more spacing
                    label_x, label_y = find_non_overlapping_position(date_pos, price_pos,
                                                                    width=0.1, height=0.07,
                                                                    x_shift_options=x_shift_options,
                                                                    y_shift_options=y_shift_options)

                    # Convert back to data coordinates
                    label_date = data.index[0] + pd.Timedelta(seconds=label_x * date_range)
                    label_price = data['Close'].min() + (label_y * price_range)

                    # Add the annotation with the optimized position
                    fig.add_annotation(
                        x=label_date,
                        y=label_price,
                        text=f"${end_price:.2f}",
                        showarrow=True,
                        arrowhead=2,
                        arrowsize=1,
                        arrowwidth=1,
                        arrowcolor="green",
                        font=dict(size=10, color='green'),
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="green",
                        borderwidth=1,
                        borderpad=2,
                        xanchor="center",
                        yanchor="middle",
                        ax=20 * (label_x - date_pos),  # Adjust arrow direction based on position
                        ay=20 * (label_y - price_pos)  # Adjust arrow direction based on position
                    )
            except Exception as e:
                print(f"Error adding surge visualization elements: {e}")

            # Add surge line and percentage annotation if we have all required variables
            try:
                if all(var is not None for var in [start_date, end_date, start_price, end_price]):
                    # Add surge line
                    fig.add_trace(go.Scatter(
                        x=[start_date, end_date],
                        y=[start_price, end_price],
                        mode='lines',
                        line=dict(color='green', width=2, dash='dot'),
                        name='Surge Trend',
                        hoverinfo='skip'
                    ))

                    # Add percentage annotation

                    # Make sure percent_change is defined
                    if percent_change is None and start_price > 0:
                        percent_change = ((end_price - start_price) / start_price) * 100

                    if percent_change is not None:
                        # Use our anti-overlap function to find a good position for the percentage annotation
                        # Calculate a midpoint position for the surge trend line
                        surge_midpoint = start_date + (end_date - start_date) / 2

                        # Convert date to a normalized position (0-1 range) for our algorithm
                        date_range = (data.index[-1] - data.index[0]).total_seconds()
                        date_pos = (surge_midpoint - data.index[0]).total_seconds() / date_range if date_range > 0 else 0.5

                        # Calculate the y-position on the trend line at the midpoint
                        x_ratio = 0.5  # Midpoint of the trend line
                        trend_y = start_price + (end_price - start_price) * x_ratio

                        # Convert price to a normalized position (0-1 range)
                        price_range = data['Close'].max() - data['Close'].min()
                        price_pos = (trend_y - data['Close'].min()) / price_range if price_range > 0 else 0.5

                        # Find a non-overlapping position, with preference for positions above the trend line
                        y_shift_options = [0.05, 0.08, 0.1, 0.15, 0.2, -0.05, -0.08, -0.1]
                        label_x, label_y = find_non_overlapping_position(date_pos, price_pos,
                                                                        width=0.08, height=0.05,
                                                                        y_shift_options=y_shift_options)

                        # Convert back to data coordinates
                        label_date = data.index[0] + pd.Timedelta(seconds=label_x * date_range)
                        label_price = data['Close'].min() + (label_y * price_range)

                        # Add the annotation with the optimized position
                        fig.add_annotation(
                            x=label_date,
                            y=label_price,
                            text=f"+{percent_change:.1f}%",
                            showarrow=True,
                            arrowhead=2,
                            arrowsize=1,
                            arrowwidth=2,
                            arrowcolor="green",
                            font=dict(size=14, color='green', weight='bold'),
                            bgcolor="rgba(255, 255, 255, 0.9)",
                            bordercolor="green",
                            borderwidth=1,
                            borderpad=4,
                            xanchor="center",
                            yanchor="middle",
                            ax=20 * (label_x - date_pos),  # Adjust arrow direction based on position
                            ay=20 * (label_y - price_pos)  # Adjust arrow direction based on position
                        )
            except Exception as e:
                print(f"Error adding surge line or annotation: {e}")

            # Add drawdown information if available
            try:
                # Check for both lowercase (from surge_analyzer.py) and uppercase (from CSV) keys
                drawdown_peak_date_str = surge_info.get('drawdown_peak_date') or surge_info.get('Drawdown Peak Date')
                drawdown_trough_date_str = surge_info.get('drawdown_trough_date') or surge_info.get('Drawdown Trough Date')
                max_drawdown = surge_info.get('max_drawdown_percent') or surge_info.get('Max Drawdown %')

                print(f"DEBUG: Drawdown information from data:")
                print(f"DEBUG: drawdown_peak_date_str = {drawdown_peak_date_str}")
                print(f"DEBUG: drawdown_trough_date_str = {drawdown_trough_date_str}")
                print(f"DEBUG: max_drawdown = {max_drawdown}")

                print(f"Drawdown peak date from surge info: {drawdown_peak_date_str}")
                print(f"Drawdown trough date from surge info: {drawdown_trough_date_str}")
                print(f"Max drawdown percent from surge info: {max_drawdown}")
            except Exception as e:
                print(f"Error getting drawdown information: {e}")

            if drawdown_peak_date_str and drawdown_trough_date_str and max_drawdown is not None and not data.empty:
                    # Convert dates to datetime objects with consistent timezone
                    try:
                        # Convert dates to datetime objects and ensure they're timezone-aware
                        peak_date = pd.to_datetime(drawdown_peak_date_str)
                        trough_date = pd.to_datetime(drawdown_trough_date_str)

                        # Make the dates timezone-aware if the data index is timezone-aware
                        if data.index.tz is not None:
                            if peak_date.tz is None:
                                peak_date = peak_date.tz_localize(data.index.tz)
                            if trough_date.tz is None:
                                trough_date = trough_date.tz_localize(data.index.tz)
                        else:
                            # If data index is timezone-naive, make the dates timezone-naive too
                            if peak_date.tz is not None:
                                peak_date = peak_date.tz_localize(None)
                            if trough_date.tz is not None:
                                trough_date = trough_date.tz_localize(None)

                        print(f"Actual drawdown period from data: {peak_date} to {trough_date}")

                        # Get the surge period dates for comparison
                        surge_start = start_date if start_date is not None else data.index[0]
                        surge_end = end_date if end_date is not None else data.index[-1]

                        # Check if the dates are in the correct order (peak should be before trough for visualization)
                        # If they're reversed, swap them for visualization purposes
                        dates_reversed = False
                        if peak_date > trough_date:
                            print(f"DEBUG: Drawdown dates are reversed: peak {peak_date} is after trough {trough_date}")
                            # Swap the dates for visualization purposes
                            peak_date, trough_date = trough_date, peak_date
                            dates_reversed = True
                            print(f"DEBUG: After swapping: peak {peak_date} to trough {trough_date}")

                        # Find the closest dates in our data
                        if peak_date < data.index.min():
                            peak_date = data.index.min()
                        if trough_date > data.index.max():
                            trough_date = data.index.max()

                        # Find the closest dates in our data
                        peak_date = data.index[data.index.get_indexer([peak_date], method='nearest')[0]]
                        trough_date = data.index[data.index.get_indexer([trough_date], method='nearest')[0]]

                        # Check if the drawdown period is before the surge period
                        # If so, we'll only use it if it's close to the surge start (within 20% of the surge period)
                        if peak_date < surge_start and trough_date < surge_start:
                            print(f"DEBUG: Drawdown period is entirely before surge period")
                            # Calculate how far before the surge the drawdown ends
                            time_diff = (surge_start - trough_date).total_seconds()
                            surge_period_length = (surge_end - surge_start).total_seconds()

                            # If the drawdown is too far before the surge (more than 20% of surge length),
                            # we'll recalculate it within or after the surge period
                            if surge_period_length > 0 and time_diff > (surge_period_length * 0.2):
                                print(f"DEBUG: Drawdown period is too far before surge period. Will recalculate.")
                                # Set flag to recalculate in the exception handler
                                raise ValueError("Drawdown period is too far before surge period")

                        # Get the prices at those dates
                        peak_price = float(data.loc[peak_date, 'Close'])
                        trough_price = float(data.loc[trough_date, 'Close'])

                        # Calculate the drawdown percentage
                        drawdown_percent = ((trough_price - peak_price) / peak_price) * 100

                        print(f"Using actual drawdown period: {peak_date} to {trough_date}")
                        print(f"Prices: {peak_price:.2f} to {trough_price:.2f}")
                        print(f"Drawdown percentage: {drawdown_percent:.2f}%")

                        # Use these actual dates and prices for visualization
                        actual_peak_date = peak_date
                        actual_trough_date = trough_date
                        actual_peak_price = peak_price
                        actual_trough_price = trough_price
                        actual_drawdown_percent = drawdown_percent
                    except Exception as e:
                        print(f"Error processing drawdown dates: {e}")
                        # Try to find the actual drawdown period by analyzing the data
                        # This is a more accurate fallback than using fixed percentages
                        try:
                            # Get the surge period dates for comparison
                            surge_start = start_date if start_date is not None else data.index[0]
                            surge_end = end_date if end_date is not None else data.index[-1]

                            # Use the full dataset to find the maximum drawdown
                            # This is more accurate than restricting to the surge period
                            print(f"Finding maximum drawdown in the entire dataset")

                            # Calculate rolling maximum and drawdown
                            prices = data['Close']
                            rolling_max = prices.cummax()
                            drawdown = (prices / rolling_max - 1.0) * 100

                            # Find the maximum drawdown
                            max_drawdown_value = drawdown.min()
                            trough_idx = drawdown.idxmin()

                            # Find the peak (highest point before the trough)
                            # We need to find the highest price before the trough
                            if trough_idx in data.index:
                                # Get data up to the trough
                                data_to_trough = data.loc[:trough_idx]
                                if not data_to_trough.empty:
                                    # Find the peak (highest price before the trough)
                                    peak_idx = data_to_trough['Close'].idxmax()
                                else:
                                    # Fallback if data_to_trough is empty
                                    peak_idx = data.index[0]
                            else:
                                # Fallback if trough_idx is not in the index
                                peak_idx = rolling_max.idxmax()

                            # Get the actual dates
                            actual_peak_date = peak_idx
                            actual_trough_date = trough_idx

                            # Get the prices at those dates
                            actual_peak_price = float(data.loc[actual_peak_date, 'Close'])
                            actual_trough_price = float(data.loc[actual_trough_date, 'Close'])

                            # Calculate the drawdown percentage
                            actual_drawdown_percent = ((actual_trough_price - actual_peak_price) / actual_peak_price) * 100

                            # Check if the drawdown is significant (at least 5%)
                            if abs(actual_drawdown_percent) < 5.0:
                                print(f"Drawdown is too small ({actual_drawdown_percent:.2f}%), looking for a more significant one")

                                # Find all significant drawdowns (more than 5%)
                                significant_drawdowns = []

                                # Scan through the data to find all local peaks and troughs
                                for i in range(1, len(prices) - 1):
                                    # If this is a local peak (higher than both neighbors)
                                    if prices.iloc[i] > prices.iloc[i-1] and prices.iloc[i] > prices.iloc[i+1]:
                                        peak_price = prices.iloc[i]
                                        peak_date = prices.index[i]

                                        # Look for the next trough
                                        for j in range(i+1, len(prices)):
                                            # If this is a local trough (lower than both neighbors)
                                            if j < len(prices) - 1 and prices.iloc[j] < prices.iloc[j-1] and prices.iloc[j] < prices.iloc[j+1]:
                                                trough_price = prices.iloc[j]
                                                trough_date = prices.index[j]

                                                # Calculate drawdown
                                                local_drawdown = ((trough_price - peak_price) / peak_price) * 100

                                                # If significant, add to our list
                                                if local_drawdown <= -5.0:  # 5% or more decline
                                                    significant_drawdowns.append({
                                                        'peak_date': peak_date,
                                                        'trough_date': trough_date,
                                                        'peak_price': peak_price,
                                                        'trough_price': trough_price,
                                                        'drawdown_percent': local_drawdown
                                                    })
                                                break

                                # If we found significant drawdowns, use the largest one
                                if significant_drawdowns:
                                    # Sort by drawdown percentage (most negative first)
                                    significant_drawdowns.sort(key=lambda x: x['drawdown_percent'])
                                    best_drawdown = significant_drawdowns[0]

                                    actual_peak_date = best_drawdown['peak_date']
                                    actual_trough_date = best_drawdown['trough_date']
                                    actual_peak_price = best_drawdown['peak_price']
                                    actual_trough_price = best_drawdown['trough_price']
                                    actual_drawdown_percent = best_drawdown['drawdown_percent']

                            print(f"Calculated actual drawdown period: {actual_peak_date} to {actual_trough_date}")
                            print(f"Actual drawdown prices: {actual_peak_price:.2f} to {actual_trough_price:.2f}")
                            print(f"Actual drawdown percentage: {actual_drawdown_percent:.2f}%")
                        except Exception as e2:
                            print(f"Error calculating actual drawdown: {e2}")
                            # Fall back to using a portion of the data for visualization
                            data_range = len(data)
                            peak_idx = int(data_range * 0.4)    # 40% from the start
                            trough_idx = int(data_range * 0.6)  # 60% from the start (40% from the end)

                            # Make sure we have valid indices
                            peak_idx = max(0, min(peak_idx, data_range - 1))
                            trough_idx = max(0, min(trough_idx, data_range - 1))

                            # Get the actual dates at these positions
                            actual_peak_date = data.index[peak_idx]
                            actual_trough_date = data.index[trough_idx]

                            # Get the prices at those dates
                            actual_peak_price = float(data.loc[actual_peak_date, 'Close'])
                            actual_trough_price = float(data.loc[actual_trough_date, 'Close'])

                            # Calculate the drawdown percentage
                            actual_drawdown_percent = ((actual_trough_price - actual_peak_price) / actual_peak_price) * 100

                            print(f"Falling back to estimated drawdown period: {actual_peak_date} to {actual_trough_date}")
                            print(f"Estimated prices: {actual_peak_price:.2f} to {actual_trough_price:.2f}")
                            print(f"Estimated drawdown percentage: {actual_drawdown_percent:.2f}%")
            else:
                # If we don't have drawdown dates, use a portion of the data for visualization
                data_range = len(data)
                peak_idx = int(data_range * 0.4)    # 40% from the start
                trough_idx = int(data_range * 0.6)  # 60% from the start (40% from the end)

                # Make sure we have valid indices
                peak_idx = max(0, min(peak_idx, data_range - 1))
                trough_idx = max(0, min(trough_idx, data_range - 1))

                # Get the actual dates at these positions
                actual_peak_date = data.index[peak_idx]
                actual_trough_date = data.index[trough_idx]

                # Get the prices at those dates
                actual_peak_price = float(data.loc[actual_peak_date, 'Close'])
                actual_trough_price = float(data.loc[actual_trough_date, 'Close'])

                # Calculate the drawdown percentage
                actual_drawdown_percent = ((actual_trough_price - actual_peak_price) / actual_peak_price) * 100

                print(f"Using estimated drawdown period: {actual_peak_date} to {actual_trough_date}")
                print(f"Estimated prices: {actual_peak_price:.2f} to {actual_trough_price:.2f}")
                print(f"Estimated drawdown percentage: {actual_drawdown_percent:.2f}%")

            # Add drawdown period highlight with improved visibility if we have valid dates
            if actual_peak_date is not None and actual_trough_date is not None:
                try:
                    print(f"DEBUG: Adding drawdown rectangle from {actual_peak_date} to {actual_trough_date}")

                    # Get the surge period dates for comparison
                    surge_start = start_date if start_date is not None else data.index[0]
                    surge_end = end_date if end_date is not None else data.index[-1]

                    # Add the drawdown rectangle with built-in annotation
                    fig.add_vrect(
                        x0=actual_peak_date,
                        x1=actual_trough_date,
                        fillcolor="rgba(255, 0, 0, 0.2)",  # Slightly more opaque for better visibility
                        opacity=0.7,                       # Increased opacity
                        layer="below",
                        line_width=1,                      # Add a thin border
                        line_color="rgba(200, 0, 0, 0.5)", # Border color
                        annotation_text="Max Drawdown",
                        annotation_position="top right",
                        annotation_font=dict(size=12, color="darkred", family="Arial Black"),
                        # Ensure the rectangle stays within the plot area
                        yref="paper",
                        y0=0,
                        y1=1
                    )

                    # We're now using the built-in annotation in the vrect
                except Exception as e:
                    print(f"Error adding drawdown highlight: {e}")

            # Add drawdown peak and trough markers if we have valid data
            try:
                if actual_peak_date is not None and actual_peak_price is not None:
                    print(f"DEBUG: Adding drawdown peak marker at {actual_peak_date}, price: {actual_peak_price}")
                    # Add marker without text
                    fig.add_trace(go.Scatter(
                        x=[actual_peak_date],
                        y=[actual_peak_price],
                        mode='markers',
                        name='Drawdown Peak',
                        marker=dict(color='#ff7f0e', size=10, symbol='triangle-down', line=dict(width=2, color='white')),
                        hoverinfo='text',
                        hovertext=f"Drawdown Peak: {actual_peak_date.strftime('%Y-%m-%d')}<br>Price: ${actual_peak_price:.2f}"
                    ))

                    # Use our anti-overlap function to find a good position for the annotation
                    # Convert date to a normalized position (0-1 range) for our algorithm
                    date_range = (data.index[-1] - data.index[0]).total_seconds()
                    date_pos = (actual_peak_date - data.index[0]).total_seconds() / date_range if date_range > 0 else 0.5

                    # Convert price to a normalized position (0-1 range)
                    price_range = data['Close'].max() - data['Close'].min()
                    price_pos = (actual_peak_price - data['Close'].min()) / price_range if price_range > 0 else 0.5

                    # For drawdown peak, prefer positions to the left and above
                    x_shift_options = [-0.03, -0.05, -0.07, 0.03, 0.05, 0.07]
                    y_shift_options = [0.05, 0.08, 0.1, 0.15, -0.05, -0.08]

                    # Find a non-overlapping position with larger width/height to ensure more spacing
                    label_x, label_y = find_non_overlapping_position(date_pos, price_pos,
                                                                    width=0.1, height=0.07,
                                                                    x_shift_options=x_shift_options,
                                                                    y_shift_options=y_shift_options)

                    # Convert back to data coordinates
                    label_date = data.index[0] + pd.Timedelta(seconds=label_x * date_range)
                    label_price = data['Close'].min() + (label_y * price_range)

                    # Add the annotation with the optimized position
                    fig.add_annotation(
                        x=label_date,
                        y=label_price,
                        text=f"${actual_peak_price:.2f}",
                        showarrow=True,
                        arrowhead=2,
                        arrowsize=1,
                        arrowwidth=1,
                        arrowcolor="#ff7f0e",
                        font=dict(size=10, color='#ff7f0e'),
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="#ff7f0e",
                        borderwidth=1,
                        borderpad=2,
                        xanchor="center",
                        yanchor="middle",
                        ax=20 * (label_x - date_pos),  # Adjust arrow direction based on position
                        ay=20 * (label_y - price_pos)  # Adjust arrow direction based on position
                    )

                if actual_trough_date is not None and actual_trough_price is not None:
                    print(f"DEBUG: Adding drawdown trough marker at {actual_trough_date}, price: {actual_trough_price}")
                    # Add marker without text
                    fig.add_trace(go.Scatter(
                        x=[actual_trough_date],
                        y=[actual_trough_price],
                        mode='markers',
                        name='Drawdown Trough',
                        marker=dict(color='#9467bd', size=10, symbol='triangle-up', line=dict(width=2, color='white')),
                        hoverinfo='text',
                        hovertext=f"Drawdown Trough: {actual_trough_date.strftime('%Y-%m-%d')}<br>Price: ${actual_trough_price:.2f}"
                    ))

                    # Use our anti-overlap function to find a good position for the annotation
                    # Convert date to a normalized position (0-1 range) for our algorithm
                    date_range = (data.index[-1] - data.index[0]).total_seconds()
                    date_pos = (actual_trough_date - data.index[0]).total_seconds() / date_range if date_range > 0 else 0.5

                    # Convert price to a normalized position (0-1 range)
                    price_range = data['Close'].max() - data['Close'].min()
                    price_pos = (actual_trough_price - data['Close'].min()) / price_range if price_range > 0 else 0.5

                    # For drawdown trough, prefer positions to the right and below
                    x_shift_options = [0.03, 0.05, 0.07, -0.03, -0.05, -0.07]
                    y_shift_options = [-0.05, -0.08, -0.1, -0.15, 0.05, 0.08]

                    # Find a non-overlapping position with larger width/height to ensure more spacing
                    label_x, label_y = find_non_overlapping_position(date_pos, price_pos,
                                                                    width=0.1, height=0.07,
                                                                    x_shift_options=x_shift_options,
                                                                    y_shift_options=y_shift_options)

                    # Convert back to data coordinates
                    label_date = data.index[0] + pd.Timedelta(seconds=label_x * date_range)
                    label_price = data['Close'].min() + (label_y * price_range)

                    # Add the annotation with the optimized position
                    fig.add_annotation(
                        x=label_date,
                        y=label_price,
                        text=f"${actual_trough_price:.2f}",
                        showarrow=True,
                        arrowhead=2,
                        arrowsize=1,
                        arrowwidth=1,
                        arrowcolor="#9467bd",
                        font=dict(size=10, color='#9467bd'),
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="#9467bd",
                        borderwidth=1,
                        borderpad=2,
                        xanchor="center",
                        yanchor="middle",
                        ax=20 * (label_x - date_pos),  # Adjust arrow direction based on position
                        ay=20 * (label_y - price_pos)  # Adjust arrow direction based on position
                    )
            except Exception as e:
                print(f"Error adding drawdown peak/trough markers: {e}")

            # Add drawdown line and annotation if we have valid data
            try:
                if all(var is not None for var in [actual_peak_date, actual_trough_date, actual_peak_price, actual_trough_price]):
                    print(f"DEBUG: Adding drawdown line from {actual_peak_date} to {actual_trough_date}")
                    # Add drawdown line
                    fig.add_trace(go.Scatter(
                        x=[actual_peak_date, actual_trough_date],
                        y=[actual_peak_price, actual_trough_price],
                        mode='lines',
                        line=dict(color='red', width=2, dash='dash'),
                        name='Drawdown',
                        hoverinfo='skip'
                    ))

                    # Add drawdown percentage annotation

                    # Make sure actual_drawdown_percent is defined
                    if actual_drawdown_percent is None and actual_peak_price > 0:
                        actual_drawdown_percent = ((actual_trough_price - actual_peak_price) / actual_peak_price) * 100
            except Exception as e:
                print(f"Error adding drawdown line or annotation: {e}")

            # Add drawdown percentage annotation if we have valid data
            try:
                if all(var is not None for var in [actual_peak_date, actual_trough_date, actual_peak_price, actual_trough_price, max_drawdown]):
                    print(f"DEBUG: Adding drawdown annotation")

                    # Get the surge period dates for comparison
                    surge_start = start_date if start_date is not None else data.index[0]
                    surge_end = end_date if end_date is not None else data.index[-1]

                    # Use our anti-overlap function to find a good position for the drawdown percentage annotation
                    # Calculate a midpoint position for the drawdown trend line
                    drawdown_midpoint = actual_peak_date + (actual_trough_date - actual_peak_date) / 2

                    # Convert date to a normalized position (0-1 range) for our algorithm
                    date_range = (data.index[-1] - data.index[0]).total_seconds()
                    date_pos = (drawdown_midpoint - data.index[0]).total_seconds() / date_range if date_range > 0 else 0.5

                    # Calculate the y-position on the trend line at the midpoint
                    x_ratio = 0.5  # Midpoint of the trend line
                    trend_y = actual_peak_price + (actual_trough_price - actual_peak_price) * x_ratio

                    # Convert price to a normalized position (0-1 range)
                    price_range = data['Close'].max() - data['Close'].min()
                    price_pos = (trend_y - data['Close'].min()) / price_range if price_range > 0 else 0.5

                    # Find a non-overlapping position, with preference for positions below the trend line
                    y_shift_options = [-0.05, -0.08, -0.1, -0.15, -0.2, 0.05, 0.08, 0.1]
                    label_x, label_y = find_non_overlapping_position(date_pos, price_pos,
                                                                    width=0.08, height=0.05,
                                                                    y_shift_options=y_shift_options)

                    # Convert back to data coordinates
                    label_date = data.index[0] + pd.Timedelta(seconds=label_x * date_range)
                    label_price = data['Close'].min() + (label_y * price_range)

                    # Add the annotation with the optimized position
                    fig.add_annotation(
                        x=label_date,
                        y=label_price,
                        text=f"{float(max_drawdown):.1f}%",
                        showarrow=True,
                        arrowhead=2,
                        arrowsize=1,
                        arrowwidth=2,
                        arrowcolor="red",
                        font=dict(size=14, color='red', weight='bold'),
                        bgcolor="rgba(255, 255, 255, 0.9)",
                        bordercolor="red",
                        borderwidth=1,
                        borderpad=4,
                        xanchor="center",
                        yanchor="middle",
                        ax=20 * (label_x - date_pos),  # Adjust arrow direction based on position
                        ay=20 * (label_y - price_pos)  # Adjust arrow direction based on position
                    )
            except Exception as e:
                print(f"Error adding drawdown percentage annotation: {e}")

        # Add moving averages
        if len(data) > 50:  # Only add if we have enough data
            # 50-day moving average
            data['MA50'] = data['Close'].rolling(window=50).mean()
            fig.add_trace(go.Scatter(
                x=data.index,
                y=data['MA50'],
                mode='lines',
                name='50-Day MA',
                line=dict(color='rgba(255, 165, 0, 0.7)', width=1.5)
            ))

            # 200-day moving average
            if len(data) > 200:
                data['MA200'] = data['Close'].rolling(window=200).mean()
                fig.add_trace(go.Scatter(
                    x=data.index,
                    y=data['MA200'],
                    mode='lines',
                    name='200-Day MA',
                    line=dict(color='rgba(128, 0, 128, 0.7)', width=1.5)
                ))

        # Update layout with improved styling
        fig.update_layout(
            title={
                'text': f"{ticker} Price History and Surge Analysis",
                'font': {'size': 24, 'color': '#2c3e50'},
                'x': 0.5,
                'xanchor': 'center',
                'y': 0.99,  # Position title at the top (valid range is 0-1)
                'yanchor': 'top'
            },
            xaxis={
                'title': None,  # Removed 'Date' title
                'showgrid': True,
                'gridcolor': 'rgba(220, 220, 220, 0.8)',
                'tickfont': {'size': 12}
            },
            yaxis={
                'title': "Price ($)",
                'title_font': {'size': 16},
                'side': "left",
                'showgrid': True,
                'gridcolor': 'rgba(220, 220, 220, 0.8)',
                'tickfont': {'size': 12},
                'tickprefix': '$'
            },
            yaxis2={
                'title': "Volume",
                'title_font': {'size': 14},
                'side': "right",
                'overlaying': "y",
                'showgrid': False,
                'rangemode': "nonnegative",
                'tickfont': {'size': 12}
            },
            hovermode="x unified",
            legend={
                'orientation': "h",
                'yanchor': "top",
                'y': 1.2,  # Position just below the top (valid range is 0-1)
                'xanchor': "center",
                'x': 0.5,
                'bgcolor': 'rgba(255, 255, 255, 0.8)',
                'bordercolor': 'rgba(0, 0, 0, 0.1)',
                'borderwidth': 1
            },
            plot_bgcolor='rgba(250, 250, 250, 0.9)',
            paper_bgcolor='rgba(255, 255, 255, 0)',
            height=700,
            margin=dict(l=60, r=60, t=150, b=60),  # Further increased top margin for both title and legend
            shapes=[]
        )

        # Add range slider with improved styling and functionality
        fig.update_layout(
            xaxis=dict(
                rangeslider=dict(
                    visible=True,
                    thickness=0.15,  # Increased thickness for better visibility
                    bgcolor="rgba(211, 211, 211, 0.7)",
                    bordercolor="rgba(0, 0, 0, 0.2)",
                    borderwidth=1
                ),
                type="date",
                automargin=True,  # Ensure date labels are fully visible
                title=None  # Ensure no title appears on the rangeslider
            )
        )

        return fig
    except Exception as e:
        # Return a basic figure with error message if anything goes wrong
        print(f"Error creating price chart: {e}")
        fig = go.Figure()
        fig.update_layout(
            title=f"Error creating chart for {ticker}",
            annotations=[{
                'text': f"An error occurred: {str(e)}",
                'showarrow': False,
                'font': {'size': 14}
            }]
        )
        return fig


def create_dashboard(results_path=None):
    """
    Create a Dash application for visualizing stock surge analysis results.

    Args:
        results_path: Path to the results file

    Returns:
        Dash application object
    """
    # Get the latest results file if not specified
    if not results_path:
        results_path = get_latest_results_file()

    if not results_path:
        print("No results file found. Please run the analysis first.")
        return None

    # Load results
    results_df = load_results(results_path)

    if results_df.empty:
        print(f"No data found in {results_path}")
        return None

    # Check if we need to convert column names to match the expected format
    # If we have lowercase keys from surge_analyzer.py, convert them to the format expected by the dashboard
    if 'total_change_percent' in results_df.columns and 'Total Change %' not in results_df.columns:
        print("Converting column names to dashboard format")
        column_mapping = {
            'total_change_percent': 'Total Change %',
            'annualized_return_percent': 'Annualized Return %',
            'max_drawdown_percent': 'Max Drawdown %',
            'volatility_percent': 'Volatility %',
            'days_analyzed': 'Days Analyzed',
            'start_date': 'Start Date',
            'end_date': 'End Date',
            'start_price': 'Start Price',
            'end_price': 'End Price',
            'drawdown_peak_date': 'Drawdown Peak Date',
            'drawdown_trough_date': 'Drawdown Trough Date'
        }
        # Only rename columns that exist
        columns_to_rename = {k: v for k, v in column_mapping.items() if k in results_df.columns}
        results_df = results_df.rename(columns=columns_to_rename)

    print(f"Results columns: {results_df.columns}")

    # Ensure all numeric columns are properly formatted
    numeric_cols = ['Total Change %', 'Annualized Return %', 'Days Analyzed',
                   'Start Price', 'End Price', 'Volatility %', 'Max Drawdown %']

    for col in numeric_cols:
        if col in results_df.columns:
            results_df[col] = pd.to_numeric(results_df[col], errors='coerce')

    # Create Dash application with a modern theme
    app = Dash(__name__, external_stylesheets=[dbc.themes.FLATLY])

    # Custom CSS for better styling with enhanced design
    app.index_string = '''
    <!DOCTYPE html>
    <html>
        <head>
            {%metas%}
            <title>Stock Surge Analysis Dashboard</title>
            {%favicon%}
            {%css%}
            <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
            <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
            <style>
                :root {
                    --primary-color: #2c3e50;
                    --secondary-color: #3498db;
                    --accent-color: #e74c3c;
                    --success-color: #2ecc71;
                    --warning-color: #f39c12;
                    --info-color: #3498db;
                    --light-color: #ecf0f1;
                    --dark-color: #2c3e50;
                    --gradient-primary: linear-gradient(135deg, #1a5276 0%, #2980b9 100%);
                    --gradient-secondary: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
                    --gradient-accent: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
                    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
                    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15);
                    --radius-sm: 4px;
                    --radius-md: 8px;
                    --radius-lg: 16px;
                    --transition-fast: all 0.2s ease;
                    --transition-normal: all 0.3s ease;
                    --transition-slow: all 0.5s ease;
                }

                body {
                    font-family: 'Poppins', sans-serif;
                    background-color: #f5f7fa;
                    color: var(--primary-color);
                    margin: 0;
                    padding: 0;
                    overflow-x: hidden;
                }

                h1, h2, h3, h4, h5, h6 {
                    font-family: 'Montserrat', sans-serif;
                    font-weight: 600;
                }

                .card {
                    box-shadow: var(--shadow-md);
                    border: none;
                    border-radius: var(--radius-md);
                    transition: var(--transition-normal);
                    overflow: hidden;
                }

                .card:hover {
                    box-shadow: var(--shadow-lg);
                    transform: translateY(-5px);
                }

                .card-header {
                    border-bottom: none;
                    padding: 1.25rem 1.5rem;
                    font-weight: 600;
                }

                .card-body {
                    padding: 1.5rem;
                }

                .stat-card {
                    border-radius: var(--radius-md);
                    padding: 1.5rem;
                    margin-bottom: 1.5rem;
                    background: white;
                    transition: var(--transition-normal);
                    position: relative;
                    overflow: hidden;
                    border-top: 4px solid var(--secondary-color);
                }

                .stat-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
                    z-index: 1;
                }

                .stat-card:hover {
                    transform: translateY(-7px);
                    box-shadow: var(--shadow-lg);
                }

                .stat-card.primary {
                    border-top-color: var(--secondary-color);
                }

                .stat-card.success {
                    border-top-color: var(--success-color);
                }

                .stat-card.warning {
                    border-top-color: var(--warning-color);
                }

                .stat-card.danger {
                    border-top-color: var(--accent-color);
                }

                .stat-value {
                    font-size: 28px;
                    font-weight: 700;
                    margin-bottom: 5px;
                    position: relative;
                    z-index: 2;
                }

                .stat-label {
                    font-size: 14px;
                    color: #6c757d;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    position: relative;
                    z-index: 2;
                }

                .header-section {
                    background: var(--gradient-primary);
                    color: white;
                    padding: 40px 0;
                    margin-bottom: 40px;
                    border-radius: 0 0 30px 30px;
                    position: relative;
                    overflow: hidden;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
                }

                .header-section::before {
                    content: '';
                    position: absolute;
                    top: -50%;
                    right: -50%;
                    width: 100%;
                    height: 200%;
                    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
                    transform: rotate(30deg);
                }

                .section-title {
                    position: relative;
                    padding-left: 15px;
                    margin-bottom: 25px;
                    font-weight: 600;
                }

                .section-title::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 5px;
                    background: var(--gradient-primary);
                    border-radius: 10px;
                }

                .stock-detail-card {
                    background: white;
                    border-radius: var(--radius-md);
                    padding: 0;
                    margin-bottom: 30px;
                    overflow: hidden;
                    box-shadow: var(--shadow-md);
                }

                .detail-label {
                    font-weight: 600;
                    color: #495057;
                    margin-bottom: 5px;
                    font-size: 14px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .detail-value {
                    font-size: 16px;
                    margin-bottom: 20px;
                }

                .positive-value {
                    color: var(--success-color);
                    font-weight: 600;
                }

                .negative-value {
                    color: var(--accent-color);
                    font-weight: 600;
                }

                .chart-container {
                    background: white;
                    border-radius: var(--radius-md);
                    padding: 20px;
                    box-shadow: var(--shadow-md);
                    margin-bottom: 30px;
                    transition: var(--transition-normal);
                }

                .chart-container:hover {
                    box-shadow: var(--shadow-lg);
                }

                /* Custom dropdown styling */
                .Select-control {
                    border-radius: var(--radius-sm);
                    border: 1px solid #e0e0e0;
                    box-shadow: var(--shadow-sm);
                }

                .Select-control:hover {
                    border-color: var(--secondary-color);
                }

                /* Custom scrollbar */
                ::-webkit-scrollbar {
                    width: 8px;
                    height: 8px;
                }

                ::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 10px;
                }

                ::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 10px;
                }

                ::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8;
                }

                /* Animations */
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                .fade-in {
                    animation: fadeIn 0.5s ease forwards;
                }

                /* Tooltip styling */
                .tooltip {
                    position: relative;
                    display: inline-block;
                }

                .tooltip .tooltiptext {
                    visibility: hidden;
                    width: 120px;
                    background-color: var(--dark-color);
                    color: #fff;
                    text-align: center;
                    border-radius: 6px;
                    padding: 5px;
                    position: absolute;
                    z-index: 1;
                    bottom: 125%;
                    left: 50%;
                    margin-left: -60px;
                    opacity: 0;
                    transition: opacity 0.3s;
                }

                .tooltip:hover .tooltiptext {
                    visibility: visible;
                    opacity: 1;
                }

                /* Filter controls */
                .filter-controls {
                    background: white;
                    border-radius: var(--radius-md);
                    padding: 15px;
                    margin-bottom: 20px;
                    box-shadow: var(--shadow-sm);
                }

                /* Chart legend styling */
                .legend-item {
                    display: inline-flex;
                    align-items: center;
                    margin-right: 15px;
                    font-size: 12px;
                }

                .legend-color {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    margin-right: 5px;
                }
            </style>
        </head>
        <body>
            {%app_entry%}
            <footer>
                {%config%}
                {%scripts%}
                {%renderer%}
            </footer>
        </body>
    </html>
    '''

    # Define layout with improved visual design
    app.layout = dbc.Container([
        # Header Section
        dbc.Row([
            dbc.Col([
                html.Div([
                    html.H1("Stock Surge Analysis Dashboard", className="text-center mb-2"),
                    html.P(f"Analysis results from: {os.path.basename(results_path)}",
                           className="text-center text-light opacity-75"),
                ], className="header-section")
            ], width=12)
        ]),

        # Executive Summary Section
        dbc.Row([
            dbc.Col([
                html.H3("Executive Summary", className="section-title"),
                dbc.Row([
                    # Summary Statistics Cards with enhanced styling
                    dbc.Col([
                        html.Div([
                            html.Div(className="stat-icon", children=[
                                html.I(className="fas fa-chart-line", style={
                                    'fontSize': '24px',
                                    'color': '#3498db',
                                    'marginBottom': '10px'
                                })
                            ]),
                            html.Div(f"{len(results_df)}", className="stat-value"),
                            html.Div("Stocks Analyzed", className="stat-label"),
                            html.Div(className="stat-progress", children=[
                                html.Div(className="progress", style={'height': '4px'}, children=[
                                    html.Div(className="progress-bar", style={
                                        'width': '100%',
                                        'backgroundColor': '#3498db'
                                    })
                                ])
                            ])
                        ], className="stat-card primary text-center")
                    ], width=12, md=3),

                    dbc.Col([
                        html.Div([
                            html.Div(className="stat-icon", children=[
                                html.I(className="fas fa-arrow-up", style={
                                    'fontSize': '24px',
                                    'color': '#2ecc71',
                                    'marginBottom': '10px'
                                })
                            ]),
                            html.Div(f"{results_df['Total Change %'].mean():.2f}%",
                                    className="stat-value positive-value"),
                            html.Div("Avg. Surge Percentage", className="stat-label"),
                            html.Div(className="stat-progress", children=[
                                html.Div(className="progress", style={'height': '4px'}, children=[
                                    html.Div(className="progress-bar", style={
                                        'width': f"{min(100, abs(results_df['Total Change %'].mean()))}%",
                                        'backgroundColor': '#2ecc71'
                                    })
                                ])
                            ])
                        ], className="stat-card success text-center")
                    ], width=12, md=3),

                    dbc.Col([
                        html.Div([
                            html.Div(className="stat-icon", children=[
                                html.I(className="fas fa-chart-bar", style={
                                    'fontSize': '24px',
                                    'color': '#f39c12',
                                    'marginBottom': '10px'
                                })
                            ]),
                            html.Div(f"{results_df['Annualized Return %'].mean():.2f}%",
                                    className="stat-value positive-value"),
                            html.Div("Avg. Annualized Return", className="stat-label"),
                            html.Div(className="stat-progress", children=[
                                html.Div(className="progress", style={'height': '4px'}, children=[
                                    html.Div(className="progress-bar", style={
                                        'width': f"{min(100, abs(results_df['Annualized Return %'].mean()))}%",
                                        'backgroundColor': '#f39c12'
                                    })
                                ])
                            ])
                        ], className="stat-card warning text-center")
                    ], width=12, md=3),

                    dbc.Col([
                        html.Div([
                            html.Div(className="stat-icon", children=[
                                html.I(className="fas fa-arrow-down", style={
                                    'fontSize': '24px',
                                    'color': '#e74c3c',
                                    'marginBottom': '10px'
                                })
                            ]),
                            html.Div(f"{results_df['Max Drawdown %'].mean():.2f}%",
                                    className="stat-value negative-value"),
                            html.Div("Avg. Maximum Drawdown", className="stat-label"),
                            html.Div(className="stat-progress", children=[
                                html.Div(className="progress", style={'height': '4px'}, children=[
                                    html.Div(className="progress-bar", style={
                                        'width': f"{min(100, abs(results_df['Max Drawdown %'].mean()))}%",
                                        'backgroundColor': '#e74c3c'
                                    })
                                ])
                            ])
                        ], className="stat-card danger text-center")
                    ], width=12, md=3),
                ])
            ], width=12)
        ], className="mb-4"),

        # Top Performers Section
        dbc.Row([
            dbc.Col([
                html.H3("Top Performers", className="section-title"),
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Top 10 Stocks by Performance", className="m-0"),
                        html.Div([
                            html.P("Sort by:", className="mb-1 mt-2"),
                            dcc.RadioItems(
                                id='top-performers-metric',
                                options=[
                                    {'label': 'Total Change %', 'value': 'Total Change %'},
                                    {'label': 'Annualized Return %', 'value': 'Annualized Return %'}
                                ],
                                value='Total Change %',
                                inline=True,
                                className="mb-2"
                            ),
                        ], className="mt-3")
                    ], className="d-flex flex-column"),
                    dbc.CardBody([
                        dcc.Graph(
                            id='top-performers-chart',
                            figure=px.bar(
                                results_df.sort_values('Total Change %', ascending=False).head(10),
                                x='Ticker',
                                y='Total Change %',
                                color='Total Change %',
                                color_continuous_scale='RdYlGn',
                                text='Total Change %',
                                hover_data=['Annualized Return %', 'Volatility %', 'Max Drawdown %']
                            ).update_layout(
                                height=400,
                                plot_bgcolor='rgba(0,0,0,0)',
                                paper_bgcolor='rgba(0,0,0,0)',
                                title_font_size=20,
                                title_x=0.5,
                                xaxis=dict(
                                    title='',
                                    title_font_size=16,
                                    tickangle=-45,
                                    tickfont=dict(size=12),
                                    gridcolor='rgba(220, 220, 220, 0.5)',
                                    automargin=True  # Ensure labels are fully visible
                                ),
                                yaxis=dict(
                                    title='Total Change %',
                                    title_font_size=16,
                                    tickfont=dict(size=12),
                                    gridcolor='rgba(220, 220, 220, 0.5)',
                                    # Fix axis scaling issues
                                    autorange=False,  # Disable autorange to set manual range
                                    range=[0, results_df.sort_values('Total Change %', ascending=False).head(10)['Total Change %'].max() * 1.1],  # Add 10% padding to max value
                                    automargin=True  # Ensure labels are fully visible
                                ),
                                coloraxis_colorbar=dict(
                                    title="Change %",
                                    thicknessmode="pixels", thickness=20,
                                    lenmode="pixels", len=300,
                                    yanchor="top", y=1,
                                    ticks="outside"
                                ),
                                margin=dict(l=40, r=40, t=40, b=80)
                            ).update_traces(
                                texttemplate='%{text:.1f}%',
                                textposition='outside',
                                marker_line_color='rgba(0, 0, 0, 0.3)',
                                marker_line_width=1,
                                hovertemplate='<b>%{x}</b><br>Total Change: %{y:.2f}%<br>Annualized Return: %{customdata[0]:.2f}%<br>Volatility: %{customdata[1]:.2f}%<br>Max Drawdown: %{customdata[2]:.2f}%'
                            )
                        )
                    ])
                ], className="chart-container")
            ], width=12, lg=6),

            dbc.Col([
                html.H3("Performance Metrics", className="section-title"),
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Risk vs. Return Analysis", className="m-0"),
                        html.Div([
                            html.P("Filter by market cap:", className="mb-1 mt-2"),
                            dcc.RangeSlider(
                                id='bubble-size-slider',
                                min=0,
                                max=100,
                                step=5,
                                marks={0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%'},
                                value=[0, 100],
                                className="mb-3"
                            ),
                        ], className="mt-3")
                    ], className="d-flex flex-column"),
                    dbc.CardBody([
                        dcc.Graph(
                            id='metrics-scatter',
                            figure=px.scatter(
                                results_df,
                                x='Volatility %',
                                y='Annualized Return %',
                                size='Total Change %',
                                color='Max Drawdown %',
                                hover_name='Ticker',
                                color_continuous_scale='RdYlGn_r',
                                size_max=30,  # Limit maximum bubble size
                                opacity=0.8,   # Add some transparency
                                labels={
                                    'Volatility %': 'Volatility (Risk) %',
                                    'Annualized Return %': 'Annualized Return %',
                                    'Max Drawdown %': 'Maximum Drawdown %'
                                }
                            ).update_layout(
                                height=400,
                                plot_bgcolor='rgba(0,0,0,0)',
                                paper_bgcolor='rgba(0,0,0,0)',
                                title_font_size=20,
                                title_x=0.5,
                                xaxis=dict(
                                    title_font_size=16,
                                    gridcolor='rgba(220, 220, 220, 0.5)',
                                    zerolinecolor='rgba(220, 220, 220, 0.5)',
                                    # Fix axis scaling issues
                                    autorange=True,
                                    rangemode='tozero',
                                    automargin=True,  # Ensure labels are fully visible
                                    tickformat='.1f'  # Format tick labels to 1 decimal place
                                ),
                                yaxis=dict(
                                    title_font_size=16,
                                    gridcolor='rgba(220, 220, 220, 0.5)',
                                    zerolinecolor='rgba(220, 220, 220, 0.5)',
                                    # Fix axis scaling issues
                                    autorange=True,
                                    rangemode='tozero',
                                    automargin=True,  # Ensure labels are fully visible
                                    tickformat='.1f'  # Format tick labels to 1 decimal place
                                ),
                                coloraxis_colorbar=dict(
                                    title="Max Drawdown %",
                                    thicknessmode="pixels", thickness=20,
                                    lenmode="pixels", len=300,
                                    yanchor="top", y=1,
                                    ticks="outside"
                                ),
                                legend=dict(
                                    orientation="h",
                                    yanchor="bottom",
                                    y=1.02,
                                    xanchor="right",
                                    x=1
                                ),
                                margin=dict(l=40, r=40, t=40, b=40),
                                hovermode="closest"
                            ).update_traces(
                                marker=dict(
                                    line=dict(width=1, color='DarkSlateGrey')
                                ),
                                selector=dict(mode='markers')
                            )
                        ),
                        html.Div([
                            html.P("Bubble size represents Total Change %", className="text-muted text-center mt-2")
                        ])
                    ])
                ], className="chart-container")
            ], width=12, lg=6)
        ], className="mb-4"),

        # Stock Details Section
        dbc.Row([
            dbc.Col([
                html.H3("Stock Details", className="section-title"),
                dbc.Row([
                    dbc.Col([
                        html.Label("Select Stock:", className="fw-bold"),
                        dcc.Dropdown(
                            id='stock-selector',
                            options=[{'label': ticker, 'value': ticker} for ticker in results_df['Ticker']],
                            value=results_df['Ticker'].iloc[0] if not results_df.empty else None,
                            clearable=False,
                            className="mb-3"
                        )
                    ], width=12, md=6),
                    dbc.Col([
                        html.Label("Sort By:", className="fw-bold"),
                        dcc.Dropdown(
                            id='sort-selector',
                            options=[
                                {'label': 'Total Change %', 'value': 'Total Change %'},
                                {'label': 'Annualized Return %', 'value': 'Annualized Return %'},
                                {'label': 'Volatility %', 'value': 'Volatility %'},
                                {'label': 'Max Drawdown %', 'value': 'Max Drawdown %'},
                                {'label': 'Ticker (A-Z)', 'value': 'Ticker'}
                            ],
                            value='Total Change %',
                            clearable=False,
                            className="mb-3"
                        )
                    ], width=12, md=6)
                ]),

                # Stock Details Card
                dbc.Card([
                    dbc.CardHeader([
                        html.H4(id='stock-header', className="m-0 fw-bold")
                    ], className="bg-primary text-white"),
                    dbc.CardBody([
                        dbc.Row([
                            # Performance Metrics
                            dbc.Col([
                                html.H5("Performance Metrics", className="mb-3 border-bottom pb-2"),
                                dbc.Row([
                                    dbc.Col([
                                        html.P([html.Span("Total Change: ", className="detail-label")]),
                                        html.H3(id='total-change', className="positive-value mb-3")
                                    ], width=6),
                                    dbc.Col([
                                        html.P([html.Span("Annualized Return: ", className="detail-label")]),
                                        html.H3(id='annualized-return', className="positive-value mb-3")
                                    ], width=6),
                                ]),
                                dbc.Row([
                                    dbc.Col([
                                        html.P([html.Span("Volatility: ", className="detail-label")]),
                                        html.H3(id='volatility', className="mb-3")
                                    ], width=6),
                                    dbc.Col([
                                        html.P([html.Span("Max Drawdown: ", className="detail-label")]),
                                        html.H3(id='max-drawdown', className="negative-value mb-3")
                                    ], width=6),
                                ])
                            ], width=12, md=6),

                            # Price Information
                            dbc.Col([
                                html.H5("Price Information", className="mb-3 border-bottom pb-2"),
                                dbc.Row([
                                    dbc.Col([
                                        html.P([html.Span("Start Price: ", className="detail-label")]),
                                        html.H3(id='start-price', className="mb-3")
                                    ], width=6),
                                    dbc.Col([
                                        html.P([html.Span("End Price: ", className="detail-label")]),
                                        html.H3(id='end-price', className="mb-3")
                                    ], width=6),
                                ]),
                                dbc.Row([
                                    dbc.Col([
                                        html.P([html.Span("Period: ", className="detail-label")]),
                                        html.P(id='period', className="detail-value")
                                    ], width=6),
                                    dbc.Col([
                                        html.P([html.Span("Days Analyzed: ", className="detail-label")]),
                                        html.P(id='days-analyzed', className="detail-value")
                                    ], width=6),
                                ])
                            ], width=12, md=6),
                        ])
                    ])
                ], className="mb-4 stock-detail-card"),

                # Tabs for Price Chart and Technical Analysis
                dbc.Card([
                    dbc.CardHeader(
                        dbc.Tabs([
                            dbc.Tab(label="Price History and Surge Analysis", tab_id="price-tab"),
                            dbc.Tab(label="Technical Analysis", tab_id="tech-analysis-tab")
                        ], id="chart-tabs", active_tab="price-tab", className="card-tabs")
                    ),
                    dbc.CardBody([
                        html.Div([
                            dcc.Graph(id='price-chart', style={'height': '700px'})
                        ], id="price-tab-content"),
                        html.Div([
                            # Technical Analysis Tabs
                            dbc.Tabs([
                                dbc.Tab(label="Moving Averages", tab_id="ma-tab", children=[
                                    dcc.Graph(id='ma-chart', style={'height': '700px'})
                                ]),
                                dbc.Tab(label="RSI Analysis", tab_id="rsi-tab", children=[
                                    dcc.Graph(id='rsi-chart', style={'height': '700px'})
                                ]),
                                dbc.Tab(label="Volume Analysis", tab_id="volume-tab", children=[
                                    dcc.Graph(id='volume-chart', style={'height': '700px'})
                                ]),
                                dbc.Tab(label="Support/Resistance", tab_id="sr-tab", children=[
                                    dcc.Graph(id='sr-chart', style={'height': '700px'})
                                ])
                            ], id="tech-analysis-subtabs", active_tab="ma-tab")
                        ], id="tech-analysis-tab-content", style={'display': 'none'})
                    ])
                ], className="chart-container")
            ], width=12)
        ])
    ], fluid=True, className="pb-5")

    # Define callbacks
    @callback(
        [Output('stock-header', 'children'),
         Output('total-change', 'children'),
         Output('annualized-return', 'children'),
         Output('period', 'children'),
         Output('days-analyzed', 'children'),
         Output('start-price', 'children'),
         Output('end-price', 'children'),
         Output('volatility', 'children'),
         Output('max-drawdown', 'children'),
         Output('price-chart', 'figure'),
         Output('ma-chart', 'figure'),
         Output('rsi-chart', 'figure'),
         Output('volume-chart', 'figure'),
         Output('sr-chart', 'figure'),
         Output('stock-selector', 'options'),  # Add output for updating dropdown options
         Output('stock-selector', 'value')],   # Add output for updating selected value
        [Input('stock-selector', 'value'),
         Input('sort-selector', 'value')]
    )
    def update_stock_details(selected_ticker, sort_by):
        print(f"\n=== Updating stock details for {selected_ticker} (sort by: {sort_by}) ===")

        # Update the dropdown options based on the sort selection
        if sort_by == 'Ticker':
            sorted_df = results_df.sort_values(sort_by)
        else:
            sorted_df = results_df.sort_values(sort_by, ascending=False)

        print(f"Sorted results by {sort_by}, found {len(sorted_df)} stocks")

        # Create new dropdown options with the sorted tickers
        dropdown_options = [{'label': ticker, 'value': ticker} for ticker in sorted_df['Ticker']]

        # If the selected ticker is not in the sorted list, use the first one
        if selected_ticker not in sorted_df['Ticker'].values:
            print(f"Selected ticker {selected_ticker} not in sorted list, using first ticker instead")
            selected_ticker = sorted_df['Ticker'].iloc[0]

        print(f"Using ticker: {selected_ticker}")

        # Get the data for the selected ticker
        ticker_row = results_df[results_df['Ticker'] == selected_ticker].iloc[0]

        # Convert the pandas Series to a dictionary to avoid Series truth value issues
        ticker_data = ticker_row.to_dict()
        print(f"Ticker data: {ticker_data}")

        # Fetch stock data
        print(f"Fetching stock data for {selected_ticker}")
        stock_data = fetch_stock_data(selected_ticker)

        # Create the price chart with actual historical data
        print(f"Creating price chart for {selected_ticker}")
        price_fig = create_price_chart(ticker=selected_ticker, surge_info=ticker_data)

        # Create technical indicator charts
        print(f"Creating technical indicator charts for {selected_ticker}")
        ma_fig = create_moving_average_chart(ticker=selected_ticker, data=stock_data, surge_info=ticker_data)
        rsi_fig = create_rsi_chart(ticker=selected_ticker, data=stock_data, surge_info=ticker_data)
        volume_fig = create_volume_chart(ticker=selected_ticker, data=stock_data, surge_info=ticker_data)
        sr_fig = create_support_resistance_chart(ticker=selected_ticker, data=stock_data, surge_info=ticker_data)

        # Format values for display - handle both uppercase and lowercase keys
        # First check for lowercase keys (from surge_analyzer.py)
        if 'total_change_percent' in ticker_data:
            total_change = f"{ticker_data['total_change_percent']:.2f}%"
            annualized_return = f"{ticker_data['annualized_return_percent']:.2f}%"
            period = f"{ticker_data['start_date']} to {ticker_data['end_date']}"
            days_analyzed = f"{ticker_data['days_analyzed']} days"
            start_price = f"${ticker_data['start_price']:.2f}"
            end_price = f"${ticker_data['end_price']:.2f}"
            volatility = f"{ticker_data['volatility_percent']:.2f}%"
            max_drawdown = f"{ticker_data['max_drawdown_percent']:.2f}%"
        else:
            # Fallback to uppercase keys (from older format)
            total_change = f"{ticker_data.get('Total Change %', 0):.2f}%"
            annualized_return = f"{ticker_data.get('Annualized Return %', 0):.2f}%"
            period = f"{ticker_data.get('Start Date', 'N/A')} to {ticker_data.get('End Date', 'N/A')}"
            days_analyzed = f"{ticker_data.get('Days Analyzed', 0)} days"
            start_price = f"${ticker_data.get('Start Price', 0):.2f}"
            end_price = f"${ticker_data.get('End Price', 0):.2f}"
            volatility = f"{ticker_data.get('Volatility %', 0):.2f}%"
            max_drawdown = f"{ticker_data.get('Max Drawdown %', 0):.2f}%"

        # Return the updated values
        return (
            f"{selected_ticker} - Surge Analysis",
            total_change,
            annualized_return,
            period,
            days_analyzed,
            start_price,
            end_price,
            volatility,
            max_drawdown,
            price_fig,         # Price chart figure
            ma_fig,           # Moving Averages chart
            rsi_fig,          # RSI chart
            volume_fig,       # Volume chart
            sr_fig,           # Support/Resistance chart
            dropdown_options,  # Return the updated dropdown options
            selected_ticker    # Return the selected ticker
        )

    @callback(
        Output('top-performers-chart', 'figure'),
        [Input('top-performers-metric', 'value')]
    )
    def update_top_performers(metric):
        # Sort by the selected metric
        sorted_df = results_df.sort_values(metric, ascending=False).head(10)

        # Create the bar chart
        fig = px.bar(
            sorted_df,
            x='Ticker',
            y=metric,
            color=metric,
            color_continuous_scale='RdYlGn',
            text=metric,
            hover_data=['Annualized Return %', 'Volatility %', 'Max Drawdown %']
        ).update_layout(
            height=400,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            title_font_size=20,
            title_x=0.5,
            xaxis=dict(
                title='',
                title_font_size=16,
                tickangle=-45,
                tickfont=dict(size=12),
                gridcolor='rgba(220, 220, 220, 0.5)'
            ),
            yaxis=dict(
                title=metric,
                title_font_size=16,
                tickfont=dict(size=12),
                gridcolor='rgba(220, 220, 220, 0.5)',
                autorange=False,
                range=[0, sorted_df[metric].max() * 1.1],  # Add 10% padding to max value
                automargin=True  # Ensure labels are fully visible
            ),
            coloraxis_colorbar=dict(
                title=metric,
                thicknessmode="pixels", thickness=20,
                lenmode="pixels", len=300,
                yanchor="top", y=1,
                ticks="outside"
            ),
            margin=dict(l=40, r=40, t=40, b=80)
        ).update_traces(
            texttemplate='%{text:.1f}%',
            textposition='outside',
            marker_line_color='rgba(0, 0, 0, 0.3)',
            marker_line_width=1,
            hovertemplate='<b>%{x}</b><br>' + metric + ': %{y:.2f}%<br>Annualized Return: %{customdata[0]:.2f}%<br>Volatility: %{customdata[1]:.2f}%<br>Max Drawdown: %{customdata[2]:.2f}%'
        )

        return fig

    @callback(
        Output('metrics-scatter', 'figure'),
        [Input('bubble-size-slider', 'value')]
    )
    def update_bubble_chart(size_range):
        # Calculate percentiles for filtering
        min_pct = size_range[0]
        max_pct = size_range[1]

        if min_pct == 0 and max_pct == 100:
            # Use all data
            filtered_df = results_df
        else:
            # Calculate percentile thresholds
            min_val = np.percentile(results_df['Total Change %'], min_pct)
            max_val = np.percentile(results_df['Total Change %'], max_pct)

            # Filter the dataframe
            filtered_df = results_df[
                (results_df['Total Change %'] >= min_val) &
                (results_df['Total Change %'] <= max_val)
            ]

        # Create the scatter plot
        fig = px.scatter(
            filtered_df,
            x='Volatility %',
            y='Annualized Return %',
            size='Total Change %',
            color='Max Drawdown %',
            hover_name='Ticker',
            color_continuous_scale='RdYlGn_r',
            size_max=30,  # Limit maximum bubble size
            opacity=0.8,   # Add some transparency
            labels={
                'Volatility %': 'Volatility (Risk) %',
                'Annualized Return %': 'Annualized Return %',
                'Max Drawdown %': 'Maximum Drawdown %'
            }
        ).update_layout(
            height=400,
            plot_bgcolor='rgba(0,0,0,0)',
            paper_bgcolor='rgba(0,0,0,0)',
            title_font_size=20,
            title_x=0.5,
            xaxis=dict(
                title='Volatility (Risk) %',
                title_font_size=16,
                gridcolor='rgba(220, 220, 220, 0.5)',
                zerolinecolor='rgba(220, 220, 220, 0.5)',
                autorange=False,
                range=[0, filtered_df['Volatility %'].max() * 1.1],  # Add 10% padding to max value
                automargin=True  # Ensure labels are fully visible
            ),
            yaxis=dict(
                title='Annualized Return %',
                title_font_size=16,
                gridcolor='rgba(220, 220, 220, 0.5)',
                zerolinecolor='rgba(220, 220, 220, 0.5)',
                autorange=False,
                range=[0, filtered_df['Annualized Return %'].max() * 1.1],  # Add 10% padding to max value
                automargin=True  # Ensure labels are fully visible
            ),
            coloraxis_colorbar=dict(
                title="Max Drawdown %",
                thicknessmode="pixels", thickness=20,
                lenmode="pixels", len=300,
                yanchor="top", y=1,
                ticks="outside"
            ),
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            margin=dict(l=40, r=40, t=40, b=40),
            hovermode="closest"
        ).update_traces(
            marker=dict(
                line=dict(width=1, color='DarkSlateGrey')
            ),
            selector=dict(mode='markers')
        )

        # Add a note about the filter
        if min_pct > 0 or max_pct < 100:
            fig.add_annotation(
                text=f"Showing stocks with Total Change % between {min_pct}th and {max_pct}th percentiles",
                xref="paper", yref="paper",
                x=0.5, y=1.05,
                showarrow=False,
                font=dict(size=12, color="gray"),
                align="center"
            )

        return fig

    # Callback for tab switching
    @callback(
        [Output('price-tab-content', 'style'),
         Output('tech-analysis-tab-content', 'style')],
        [Input('chart-tabs', 'active_tab')]
    )
    def switch_tab(active_tab):
        """Switch between price chart and technical analysis tabs."""
        if active_tab == 'price-tab':
            return {'display': 'block'}, {'display': 'none'}
        else:  # tech-analysis-tab
            return {'display': 'none'}, {'display': 'block'}


    return app


def run_dashboard(results_path=None, debug=True, port=8050):
    """
    Run the dashboard application.

    Args:
        results_path: Path to the results file
        debug: Whether to run in debug mode
        port: Port to run the server on
    """
    app = create_dashboard(results_path)

    if app:
        app.run(debug=debug, port=port)
    else:
        print("Failed to create dashboard. Please check the results file.")


if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Run the Stock Finder Dashboard')
    parser.add_argument('--port', type=int, default=8050, help='Port to run the dashboard on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')
    parser.add_argument('--results', type=str, help='Path to results file')

    args = parser.parse_args()

    run_dashboard(results_path=args.results, debug=args.debug, port=args.port)
