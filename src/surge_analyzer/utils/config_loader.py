"""
Configuration Loader Module

This module handles loading and merging configuration from various sources.
"""

import os
import yaml
import argparse
from typing import Dict, Any, <PERSON><PERSON>


def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """
    Load configuration from a YAML file.

    Args:
        file_path: Path to the YAML configuration file

    Returns:
        Dictionary containing the configuration
    """
    if not os.path.exists(file_path):
        return {}

    with open(file_path, 'r') as f:
        config = yaml.safe_load(f)

    return config or {}


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two configuration dictionaries, with override_config taking precedence.

    Args:
        base_config: Base configuration dictionary
        override_config: Override configuration dictionary

    Returns:
        Merged configuration dictionary
    """
    result = base_config.copy()

    for key, value in override_config.items():
        if isinstance(value, dict) and key in result and isinstance(result[key], dict):
            # Recursively merge nested dictionaries
            result[key] = merge_configs(result[key], value)
        else:
            # Override or add the value
            result[key] = value

    return result


def parse_command_line_args() -> Tuple[Dict[str, Any], str]:
    """
    Parse command line arguments into a configuration dictionary.

    Returns:
        Dictionary containing the command line arguments and the path to the user config file
    """
    parser = argparse.ArgumentParser(
        description='Find stocks with significant price surges.'
    )

    parser.add_argument(
        '--tickers', '-t',
        type=str,
        help='Comma-separated list of stock tickers to analyze'
    )

    parser.add_argument(
        '--years', '-y',
        type=int,
        help='Number of years to look back'
    )

    parser.add_argument(
        '--min-surge', '-m',
        type=float,
        help='Minimum percentage surge to identify'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output CSV file path'
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/surge_analyzer/surge_analyzer_config.yaml',
        help='Path to user configuration file'
    )

    args = parser.parse_args()

    # Convert args to dictionary, excluding None values
    args_dict = {k: v for k, v in vars(args).items() if v is not None and k != 'config'}

    # Restructure the args to match the config structure
    config = {}

    if 'years' in args_dict:
        config.setdefault('analysis', {})['years_back'] = args_dict.pop('years')

    if 'min_surge' in args_dict:
        config.setdefault('analysis', {})['min_surge_percent'] = args_dict.pop('min_surge')

    if 'output' in args_dict:
        config.setdefault('output', {})['csv_path'] = args_dict.pop('output')
        config.setdefault('output', {})['format'] = 'csv'

    # Add remaining args directly to the config
    for key, value in args_dict.items():
        config[key] = value

    return config, args.config


def apply_preset(config: Dict[str, Any], preset_name: str) -> Dict[str, Any]:
    """
    Apply a preset configuration to the current configuration.

    Args:
        config: Current configuration dictionary
        preset_name: Name of the preset to apply

    Returns:
        Updated configuration dictionary with preset values applied
    """
    result = config.copy()

    # Apply analysis preset if it exists
    if 'analysis' in result and 'presets' in result['analysis'] and preset_name in result['analysis']['presets']:
        analysis_preset = result['analysis']['presets'][preset_name]
        for key, value in analysis_preset.items():
            result['analysis'][key] = value

    # Apply output preset if it exists
    if 'output' in result and 'presets' in result['output'] and preset_name in result['output']['presets']:
        output_preset = result['output']['presets'][preset_name]
        for key, value in output_preset.items():
            result['output'][key] = value

    return result


def load_config(config_path: str = 'config/surge_analyzer/surge_analyzer_config.yaml', preset: str = None, cmd_args: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Load and merge configuration from all sources.

    Args:
        config_path: Path to the configuration file
        preset: Optional preset name to apply
        cmd_args: Optional command-line arguments as a dictionary

    Returns:
        Merged configuration dictionary
    """
    # Load configuration from file
    config = load_yaml_config(config_path)

    # Apply preset if specified
    if preset:
        config = apply_preset(config, preset)
    elif 'stock_selection' in config and 'source' in config['stock_selection']:
        # Auto-apply preset based on source if available
        source = config['stock_selection']['source']
        if source in ['sp500', 'sec_filers']:
            config = apply_preset(config, source)

    # Command line arguments have highest precedence
    if cmd_args:
        # Restructure the args to match the config structure
        cmd_config = {}

        if 'years' in cmd_args and cmd_args['years'] is not None:
            cmd_config.setdefault('analysis', {})['years_back'] = cmd_args['years']

        if 'min_surge' in cmd_args and cmd_args['min_surge'] is not None:
            cmd_config.setdefault('analysis', {})['min_surge_percent'] = cmd_args['min_surge']

        if 'output' in cmd_args and cmd_args['output'] is not None:
            cmd_config.setdefault('output', {})['csv_path'] = cmd_args['output']
            cmd_config.setdefault('output', {})['format'] = 'csv'

        if 'tickers' in cmd_args and cmd_args['tickers'] is not None:
            cmd_config['tickers'] = cmd_args['tickers']
            cmd_config['stock_selection'] = {'source': 'manual'}

        config = merge_configs(config, cmd_config)

    return config
