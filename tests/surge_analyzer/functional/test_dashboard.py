"""
Functional tests for the Surge Analyzer dashboard.
"""

import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
import plotly.graph_objects as go

from src.surge_analyzer.dashboard import (
    create_moving_average_chart,
    create_rsi_chart,
    create_volume_chart,
    create_support_resistance_chart,
    run_dashboard
)


class TestDashboard:
    """Functional tests for the Surge Analyzer dashboard."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')

        # Create a price series with a trend
        prices = [100 + i * 0.5 for i in range(100)]  # Simple uptrend

        # Create DataFrame
        self.sample_data = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Adj Close': prices,
            'Volume': [1000000 + i * 10000 for i in range(100)]
        }, index=dates)

        # Sample surge info
        self.surge_info = {
            'total_change_percent': 50.0,
            'max_drawdown_percent': -10.0,
            'volatility_percent': 15.0,
            'surge_start_date': '2023-01-15',
            'surge_end_date': '2023-03-15',
            'surge_percent_change': 30.0,
            'drawdown_peak_date': '2023-03-20',
            'drawdown_trough_date': '2023-04-01'
        }

    def test_create_moving_average_chart(self):
        """Test creating a moving average chart."""
        # Call the function
        chart = create_moving_average_chart('AAPL', self.sample_data, self.surge_info)

        # Verify the chart was created
        assert chart is not None
        assert isinstance(chart, go.Figure)

        # Test with empty data
        chart = create_moving_average_chart('AAPL', pd.DataFrame(), self.surge_info)
        assert chart is not None  # Should return an error figure

    def test_create_rsi_chart(self):
        """Test creating an RSI chart."""
        # Call the function
        chart = create_rsi_chart('AAPL', self.sample_data, self.surge_info)

        # Verify the chart was created
        assert chart is not None
        assert isinstance(chart, go.Figure)

        # Test with empty data
        chart = create_rsi_chart('AAPL', pd.DataFrame(), self.surge_info)
        assert chart is not None  # Should return an error figure

    def test_create_volume_chart(self):
        """Test creating a volume chart."""
        # Call the function
        chart = create_volume_chart('AAPL', self.sample_data, self.surge_info)

        # Verify the chart was created
        assert chart is not None
        assert isinstance(chart, go.Figure)

        # Test with empty data
        chart = create_volume_chart('AAPL', pd.DataFrame(), self.surge_info)
        assert chart is not None  # Should return an error figure

    def test_create_support_resistance_chart(self):
        """Test creating a support/resistance chart."""
        # Call the function
        chart = create_support_resistance_chart('AAPL', self.sample_data, self.surge_info)

        # Verify the chart was created
        assert chart is not None
        assert isinstance(chart, go.Figure)

        # Test with empty data
        chart = create_support_resistance_chart('AAPL', pd.DataFrame(), self.surge_info)
        assert chart is not None  # Should return an error figure

    @patch('src.surge_analyzer.dashboard.create_dashboard')
    def test_run_dashboard(self, mock_create_dashboard):
        """Test running the dashboard."""
        # Configure the mock
        mock_app = MagicMock()
        mock_create_dashboard.return_value = mock_app

        # Call the function with test parameters
        run_dashboard(
            results_path='test_results.csv',
            port=8050,
            debug=False
        )

        # Verify the app was created and run
        mock_create_dashboard.assert_called_once_with('test_results.csv')
        mock_app.run.assert_called_once_with(
            port=8050,
            debug=False
        )
