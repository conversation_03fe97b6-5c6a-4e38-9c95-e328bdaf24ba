"""
Functional tests for the Surge Analyzer pipeline.
"""

import os
import pytest
import pandas as pd
from unittest.mock import patch, MagicMock

from src.surge_analyzer.data.data_fetcher import StockDataFetcher
from src.surge_analyzer.data.stock_list_fetcher import StockListFetcher
from src.surge_analyzer.analysis.surge_analyzer import Surge<PERSON>nalyzer
from src.surge_analyzer.utils.config_loader import load_config


class TestSurgePipeline:
    """Functional tests for the Surge Analyzer pipeline."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create a temporary directory for test results
        os.makedirs('test_results', exist_ok=True)

        # Sample configuration
        self.config = {
            'analysis': {
                'years_back': 1,
                'min_surge_percent': 20
            },
            'stock_selection': {
                'source': 'manual',
                'tickers': ['AAPL', 'MSFT', 'GOOGL']
            },
            'output': {
                'format': 'csv',
                'csv_path': 'test_results/surge_stocks.csv'
            }
        }

    def teardown_method(self):
        """Tear down test fixtures."""
        # Clean up test results
        if os.path.exists('test_results/surge_stocks.csv'):
            os.remove('test_results/surge_stocks.csv')
        if os.path.exists('test_results'):
            os.rmdir('test_results')

    @pytest.mark.parametrize("min_surge_percent,expected_count", [
        (10, 3),  # All stocks should meet this threshold
        (30, 2),  # Only AAPL and MSFT should meet this threshold
        (50, 1),  # Only AAPL should meet this threshold
        (100, 0)  # No stocks should meet this threshold
    ])
    def test_surge_pipeline_with_different_thresholds(self, min_surge_percent, expected_count, sample_stock_data, mock_stock_data_fetcher, mock_stock_list_fetcher):
        """Test the surge pipeline with different surge thresholds."""
        # Update the config with the test threshold
        self.config['analysis']['min_surge_percent'] = min_surge_percent

        # Create test instances
        stock_list_fetcher = StockListFetcher()
        stock_data_fetcher = StockDataFetcher()
        surge_analyzer = SurgeAnalyzer()

        # Get stock list
        tickers = self.config['stock_selection']['tickers']

        # Create different sample data for each ticker
        stock_data_dict = {}
        for i, ticker in enumerate(tickers):
            # Modify the sample data to have different surge percentages
            data = sample_stock_data.copy()

            # AAPL: 60% surge, MSFT: 40% surge, GOOGL: 20% surge
            surge_percent = 60 - i * 20

            # Modify the prices to create the desired surge
            start_price = data['Close'].iloc[0]
            end_price = start_price * (1 + surge_percent / 100)

            # Create a linear price increase
            data_len = len(data)
            for j in range(data_len):
                progress = j / (data_len - 1)
                data.iloc[j, data.columns.get_loc('Close')] = start_price + progress * (end_price - start_price)
                data.iloc[j, data.columns.get_loc('Adj Close')] = start_price + progress * (end_price - start_price)

            stock_data_dict[ticker] = data

        # Mock the fetch_multiple_stocks method to return our custom data
        with patch.object(StockDataFetcher, 'fetch_multiple_stocks', return_value=stock_data_dict):
            # Find surge stocks
            surge_stocks = surge_analyzer.find_surge_stocks(
                stock_data_dict,
                min_surge_percent=self.config['analysis']['min_surge_percent']
            )

            # Verify the number of surge stocks matches the expected count
            assert len(surge_stocks) == expected_count

            # Verify the surge stocks are in the expected order (highest surge first)
            if expected_count > 0:
                assert list(surge_stocks.keys())[0] == 'AAPL'
            if expected_count > 1:
                assert list(surge_stocks.keys())[1] == 'MSFT'
            if expected_count > 2:
                assert list(surge_stocks.keys())[2] == 'GOOGL'

    def test_surge_pipeline_with_empty_data(self, mock_stock_data_fetcher, mock_stock_list_fetcher):
        """Test the surge pipeline with empty data."""
        # Create test instances
        stock_list_fetcher = StockListFetcher()
        stock_data_fetcher = StockDataFetcher()
        surge_analyzer = SurgeAnalyzer()

        # Create empty stock data dictionary
        stock_data_dict = {
            'AAPL': pd.DataFrame(),
            'MSFT': pd.DataFrame(),
            'GOOGL': pd.DataFrame()
        }

        # Mock the fetch_multiple_stocks method to return empty data
        with patch.object(StockDataFetcher, 'fetch_multiple_stocks', return_value=stock_data_dict):
            # Find surge stocks
            surge_stocks = surge_analyzer.find_surge_stocks(
                stock_data_dict,
                min_surge_percent=self.config['analysis']['min_surge_percent']
            )

            # Verify no surge stocks were found
            assert len(surge_stocks) == 0

    def test_surge_pipeline_with_mixed_data(self, sample_stock_data, mock_stock_data_fetcher, mock_stock_list_fetcher):
        """Test the surge pipeline with a mix of valid and empty data."""
        # Create test instances
        stock_list_fetcher = StockListFetcher()
        stock_data_fetcher = StockDataFetcher()
        surge_analyzer = SurgeAnalyzer()

        # Create stock data dictionary with mixed data
        stock_data_dict = {
            'AAPL': sample_stock_data.copy(),  # Valid data with surge
            'MSFT': pd.DataFrame(),  # Empty data
            'GOOGL': sample_stock_data.copy()  # Valid data with surge
        }

        # Mock the fetch_multiple_stocks method to return our mixed data
        with patch.object(StockDataFetcher, 'fetch_multiple_stocks', return_value=stock_data_dict):
            # Find surge stocks
            surge_stocks = surge_analyzer.find_surge_stocks(
                stock_data_dict,
                min_surge_percent=self.config['analysis']['min_surge_percent']
            )

            # Verify only the valid data stocks were found
            assert len(surge_stocks) == 2
            assert 'AAPL' in surge_stocks
            assert 'MSFT' not in surge_stocks
            assert 'GOOGL' in surge_stocks

    def test_surge_pipeline_end_to_end(self, sample_stock_data, mock_stock_data_fetcher, mock_stock_list_fetcher):
        """Test the complete surge pipeline from end to end."""
        # Create test instances
        stock_list_fetcher = StockListFetcher()
        stock_data_fetcher = StockDataFetcher()
        surge_analyzer = SurgeAnalyzer()

        # Get stock list
        tickers = self.config['stock_selection']['tickers']

        # Create stock data dictionary with guaranteed surge
        stock_data_dict = {}
        for ticker in tickers:
            # Create a copy of the sample data
            data = sample_stock_data.copy()

            # Modify the data to ensure it has a surge that meets the threshold
            # Start price
            start_price = data['Close'].iloc[0]
            # End price with 30% increase (above our 20% threshold)
            end_price = start_price * 1.3

            # Create a linear price increase
            data_len = len(data)
            for j in range(data_len):
                progress = j / (data_len - 1)
                data.iloc[j, data.columns.get_loc('Close')] = start_price + progress * (end_price - start_price)
                data.iloc[j, data.columns.get_loc('Adj Close')] = start_price + progress * (end_price - start_price)

            stock_data_dict[ticker] = data

        # Mock the fetch_multiple_stocks method to return our custom data
        with patch.object(StockDataFetcher, 'fetch_multiple_stocks', return_value=stock_data_dict):
            # Find surge stocks
            surge_stocks = surge_analyzer.find_surge_stocks(
                stock_data_dict,
                min_surge_percent=self.config['analysis']['min_surge_percent']
            )

        # Verify surge stocks were found
        assert len(surge_stocks) > 0

        # Analyze the first surge stock
        ticker = list(surge_stocks.keys())[0]
        data = stock_data_dict[ticker]
        analysis = surge_analyzer.analyze_surge_patterns(data)

        # Verify the analysis contains the expected metrics
        assert 'total_change_percent' in analysis
        assert 'max_drawdown_percent' in analysis
        assert 'volatility_percent' in analysis
        assert 'surge_start_date' in analysis
        assert 'surge_end_date' in analysis
        assert 'surge_percent_change' in analysis

        # Verify the surge percentage meets the minimum threshold
        assert analysis['surge_percent_change'] >= self.config['analysis']['min_surge_percent']
