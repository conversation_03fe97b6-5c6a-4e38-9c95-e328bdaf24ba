"""
Unit tests for the technical indicators module.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import plotly.graph_objects as go

from src.surge_analyzer.technical_analysis.technical_indicators import (
    calculate_moving_averages,
    calculate_rsi,
    calculate_bollinger_bands,
    calculate_macd,
    calculate_volume_analysis,
    create_rsi_figure,
    create_macd_figure,
    create_bollinger_bands_figure,
    create_volume_figure,
    create_price_ma_figure
)


class TestTechnicalIndicators:
    """Test cases for the technical indicators module."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create sample data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')

        # Create a price series with a trend
        prices = []
        for i in range(100):
            if i < 30:
                # First 30 days: stable around 100
                price = 100 + np.random.normal(0, 1)
            elif i < 60:
                # Next 30 days: uptrend
                trend_progress = (i - 30) / 30
                price = 100 * (1 + 0.3 * trend_progress) + np.random.normal(0, 2)
            else:
                # Last 40 days: downtrend
                trend_progress = (i - 60) / 40
                price = 130 * (1 - 0.2 * trend_progress) + np.random.normal(0, 3)
            prices.append(price)

        # Create DataFrame
        self.sample_data = pd.DataFrame({
            'Open': prices,
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Adj Close': prices,
            'Volume': [1000000 + np.random.randint(-100000, 100000) for _ in range(100)]
        }, index=dates)

        # Define surge and drawdown periods
        self.surge_period = (dates[30], dates[59])
        self.drawdown_period = (dates[60], dates[80])

    def test_calculate_moving_averages(self):
        """Test calculating moving averages."""
        result = calculate_moving_averages(self.sample_data)

        # Check that the result contains the expected columns
        assert 'MA20' in result.columns
        assert 'MA50' in result.columns
        assert 'Golden Cross' in result.columns
        assert 'Death Cross' in result.columns

        # Check that the moving averages are calculated correctly
        assert result['MA20'].iloc[25] == pytest.approx(self.sample_data['Close'].iloc[6:26].mean(), abs=0.01)
        assert result['MA50'].iloc[55] == pytest.approx(self.sample_data['Close'].iloc[6:56].mean(), abs=0.01)

    def test_calculate_rsi(self):
        """Test calculating RSI."""
        result = calculate_rsi(self.sample_data)

        # Check that the result contains the expected columns
        assert 'RSI' in result.columns
        assert 'Overbought' in result.columns
        assert 'Oversold' in result.columns

        # Check that RSI values are within the expected range
        assert all(0 <= rsi <= 100 for rsi in result['RSI'].dropna())

        # Check that overbought and oversold flags are set correctly
        assert all(result.loc[result['Overbought'], 'RSI'] > 70)
        assert all(result.loc[result['Oversold'], 'RSI'] < 30)

    def test_calculate_bollinger_bands(self):
        """Test calculating Bollinger Bands."""
        result = calculate_bollinger_bands(self.sample_data)

        # Check that the result contains the expected columns
        assert 'BB_middle' in result.columns
        assert 'BB_upper' in result.columns
        assert 'BB_lower' in result.columns
        assert 'BB_width' in result.columns

        # Check that the middle band is the 20-day moving average
        assert result['BB_middle'].iloc[25] == pytest.approx(self.sample_data['Close'].iloc[6:26].mean(), abs=0.01)

        # Check that the upper and lower bands are correctly calculated
        std_25 = self.sample_data['Close'].iloc[6:26].std()
        assert result['BB_upper'].iloc[25] == pytest.approx(result['BB_middle'].iloc[25] + 2 * std_25, abs=0.01)
        assert result['BB_lower'].iloc[25] == pytest.approx(result['BB_middle'].iloc[25] - 2 * std_25, abs=0.01)

    def test_calculate_macd(self):
        """Test calculating MACD."""
        result = calculate_macd(self.sample_data)

        # Check that the result contains the expected columns
        assert 'MACD' in result.columns
        assert 'MACD_signal' in result.columns
        assert 'MACD_histogram' in result.columns
        assert 'MACD_cross_above' in result.columns
        assert 'MACD_cross_below' in result.columns

        # Check that MACD is calculated as the difference between fast and slow EMAs
        assert result['MACD'].iloc[50] == pytest.approx(result['EMA_fast'].iloc[50] - result['EMA_slow'].iloc[50], abs=0.01)

        # Check that the histogram is calculated as the difference between MACD and signal
        assert result['MACD_histogram'].iloc[50] == pytest.approx(result['MACD'].iloc[50] - result['MACD_signal'].iloc[50], abs=0.01)

    def test_calculate_volume_analysis(self):
        """Test calculating volume analysis."""
        result = calculate_volume_analysis(self.sample_data)

        # Check that the result contains the expected columns
        assert 'Volume_MA20' in result.columns
        assert 'Volume_Spike' in result.columns
        assert 'Price_Change' in result.columns
        assert 'Volume_Change' in result.columns
        assert 'Price_Volume_Aligned' in result.columns

        # Check that volume spikes are correctly identified
        assert all(result.loc[result['Volume_Spike'], 'Volume'] > 2 * result.loc[result['Volume_Spike'], 'Volume_MA20'])

    def test_create_rsi_figure(self):
        """Test creating RSI figure."""
        fig = create_rsi_figure(self.sample_data, 'TEST', self.surge_period, self.drawdown_period)

        # Check that the figure is a Plotly figure
        assert isinstance(fig, go.Figure)

        # Check that the figure has the expected traces
        trace_names = [trace.name for trace in fig.data]
        assert 'RSI' in trace_names
        assert 'Overbought (70)' in trace_names
        assert 'Oversold (30)' in trace_names

    def test_create_macd_figure(self):
        """Test creating MACD figure."""
        fig = create_macd_figure(self.sample_data, 'TEST', self.surge_period, self.drawdown_period)

        # Check that the figure is a Plotly figure
        assert isinstance(fig, go.Figure)

        # Check that the figure has the expected traces
        trace_names = [trace.name for trace in fig.data]
        assert 'MACD' in trace_names
        assert 'Signal Line' in trace_names
        assert 'MACD Histogram' in trace_names

    def test_create_bollinger_bands_figure(self):
        """Test creating Bollinger Bands figure."""
        fig = create_bollinger_bands_figure(self.sample_data, 'TEST', self.surge_period, self.drawdown_period)

        # Check that the figure is a Plotly figure
        assert isinstance(fig, go.Figure)

        # Check that the figure has the expected traces
        trace_names = [trace.name for trace in fig.data]
        assert 'Price' in trace_names
        assert any('Upper Band' in name for name in trace_names)

    def test_create_volume_figure(self):
        """Test creating volume figure."""
        fig = create_volume_figure(self.sample_data, 'TEST', self.surge_period, self.drawdown_period)

        # Check that the figure is a Plotly figure
        assert isinstance(fig, go.Figure)

        # Check that the figure has the expected traces
        trace_names = [trace.name for trace in fig.data]
        assert 'Volume' in trace_names
        assert any('Avg Volume' in name for name in trace_names)

    def test_create_price_ma_figure(self):
        """Test creating price and moving averages figure."""
        fig = create_price_ma_figure(self.sample_data, 'TEST', self.surge_period, self.drawdown_period)

        # Check that the figure is a Plotly figure
        assert isinstance(fig, go.Figure)

        # Check that the figure has the expected traces
        trace_names = [trace.name for trace in fig.data]
        assert 'Price' in trace_names
        assert any('20' in name and 'MA' in name for name in trace_names)
        assert any('50' in name and 'MA' in name for name in trace_names)
