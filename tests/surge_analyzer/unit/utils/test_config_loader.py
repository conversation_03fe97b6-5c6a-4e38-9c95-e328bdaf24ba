"""
Unit tests for the config_loader module.
"""

import os
import pytest
import tempfile
import yaml
from unittest.mock import patch, MagicMock

from src.surge_analyzer.utils.config_loader import (
    load_yaml_config,
    merge_configs,
    parse_command_line_args,
    apply_preset,
    load_config
)


class TestConfigLoader:
    """Test cases for the config_loader module."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create a temporary directory for test files
        self.temp_dir = tempfile.TemporaryDirectory()

        # Sample configuration
        self.sample_config = {
            'analysis': {
                'years_back': 5,
                'min_surge_percent': 50,
                'presets': {
                    'aggressive': {
                        'min_surge_percent': 100
                    },
                    'conservative': {
                        'min_surge_percent': 30
                    }
                }
            },
            'output': {
                'format': 'csv',
                'csv_path': 'results/surge_stocks.csv',
                'presets': {
                    'detailed': {
                        'include_metrics': True
                    }
                }
            },
            'stock_selection': {
                'source': 'sp500'
            }
        }

    def teardown_method(self):
        """Tear down test fixtures."""
        self.temp_dir.cleanup()

    def test_load_yaml_config(self):
        """Test loading configuration from a YAML file."""
        # Create a test config file
        config_path = os.path.join(self.temp_dir.name, 'test_config.yaml')
        with open(config_path, 'w') as f:
            yaml.dump(self.sample_config, f)

        # Test loading the config
        loaded_config = load_yaml_config(config_path)

        # Verify the loaded config matches the original
        assert loaded_config == self.sample_config

        # Test loading a non-existent file
        nonexistent_path = os.path.join(self.temp_dir.name, 'nonexistent.yaml')
        loaded_config = load_yaml_config(nonexistent_path)
        assert loaded_config == {}

    def test_merge_configs(self):
        """Test merging configuration dictionaries."""
        # Base config
        base_config = {
            'analysis': {
                'years_back': 5,
                'min_surge_percent': 50
            },
            'output': {
                'format': 'csv'
            }
        }

        # Override config
        override_config = {
            'analysis': {
                'min_surge_percent': 30
            },
            'stock_selection': {
                'source': 'sp500'
            }
        }

        # Expected merged config
        expected_config = {
            'analysis': {
                'years_back': 5,
                'min_surge_percent': 30
            },
            'output': {
                'format': 'csv'
            },
            'stock_selection': {
                'source': 'sp500'
            }
        }

        # Test merging
        merged_config = merge_configs(base_config, override_config)
        assert merged_config == expected_config

    @patch('argparse.ArgumentParser.parse_args')
    def test_parse_command_line_args(self, mock_parse_args):
        """Test parsing command line arguments."""
        # Mock the parsed arguments
        mock_args = MagicMock()
        mock_args.tickers = 'AAPL,MSFT,GOOGL'
        mock_args.years = 3
        mock_args.min_surge = 40
        mock_args.output = 'results/custom_output.csv'
        mock_args.config = 'config/custom_config.yaml'
        mock_parse_args.return_value = mock_args

        # Expected config from args
        expected_config = {
            'tickers': 'AAPL,MSFT,GOOGL',
            'analysis': {
                'years_back': 3,
                'min_surge_percent': 40
            },
            'output': {
                'csv_path': 'results/custom_output.csv',
                'format': 'csv'
            }
        }

        # Test parsing
        config, config_path = parse_command_line_args()

        # Verify the parsed config
        # Extract only the keys we care about from the config
        filtered_config = {
            'tickers': config.get('tickers'),
            'analysis': config.get('analysis', {}),
            'output': config.get('output', {})
        }
        assert filtered_config == expected_config
        assert config_path == 'config/custom_config.yaml'

    def test_apply_preset(self):
        """Test applying a preset configuration."""
        # Test applying the 'aggressive' preset
        config_with_preset = apply_preset(self.sample_config, 'aggressive')
        assert config_with_preset['analysis']['min_surge_percent'] == 100

        # Test applying the 'conservative' preset
        config_with_preset = apply_preset(self.sample_config, 'conservative')
        assert config_with_preset['analysis']['min_surge_percent'] == 30

        # Test applying the 'detailed' preset
        config_with_preset = apply_preset(self.sample_config, 'detailed')
        assert config_with_preset['output']['include_metrics'] is True

        # Test applying a non-existent preset
        config_with_preset = apply_preset(self.sample_config, 'nonexistent')
        assert config_with_preset == self.sample_config

    @patch('src.surge_analyzer.utils.config_loader.load_yaml_config')
    def test_load_config(self, mock_load_yaml):
        """Test loading and merging configuration from all sources."""
        # Mock the loaded YAML config
        mock_load_yaml.return_value = self.sample_config

        # Test loading with default parameters
        config = load_config()
        assert config == self.sample_config

        # Test loading with a preset
        config = load_config(preset='aggressive')
        assert config['analysis']['min_surge_percent'] == 100

        # Test loading with command line arguments
        cmd_args = {
            'tickers': 'AAPL,MSFT',
            'years': 2,
            'min_surge': 60,
            'output': 'custom_output.csv'
        }
        config = load_config(cmd_args=cmd_args)
        assert config['tickers'] == 'AAPL,MSFT'
        assert config['analysis']['years_back'] == 2
        assert config['analysis']['min_surge_percent'] == 60
        assert config['output']['csv_path'] == 'custom_output.csv'
        assert config['stock_selection']['source'] == 'manual'
