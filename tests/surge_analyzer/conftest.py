"""
Pytest fixtures for the Surge Analyzer tests.
"""

import os
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

@pytest.fixture
def sample_stock_data():
    """
    Create a sample stock price DataFrame for testing.

    Returns:
        pd.DataFrame: Sample stock price data with OHLCV columns.
    """
    # Create a date range for the past 100 days
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=100)
    date_range = pd.date_range(start=start_date, end=end_date, freq='B')

    # Create sample data with a price surge
    np.random.seed(42)  # For reproducibility

    # Base price starts at 100
    base_price = 100

    # Create price series with some randomness
    close_prices = []
    for i in range(len(date_range)):
        if i < 30:
            # First 30 days: stable with small fluctuations
            price = base_price * (1 + np.random.normal(0, 0.01))
        elif i < 50:
            # Next 20 days: surge period (30% increase)
            surge_progress = (i - 30) / 20
            price = base_price * (1 + 0.3 * surge_progress + np.random.normal(0, 0.02))
        else:
            # Remaining days: stabilize at new level with fluctuations
            price = base_price * 1.3 * (1 + np.random.normal(0, 0.015))

        close_prices.append(price)

    # Create OHLCV data
    data = {
        'Date': date_range,
        'Open': [price * (1 - np.random.uniform(0, 0.01)) for price in close_prices],
        'High': [price * (1 + np.random.uniform(0, 0.02)) for price in close_prices],
        'Low': [price * (1 - np.random.uniform(0, 0.02)) for price in close_prices],
        'Close': close_prices,
        'Adj Close': close_prices,
        'Volume': [int(1000000 * (1 + np.random.normal(0, 0.3))) for _ in close_prices]
    }

    df = pd.DataFrame(data)
    df.set_index('Date', inplace=True)

    return df

@pytest.fixture
def sample_stock_list():
    """
    Create a sample list of stock tickers for testing.

    Returns:
        list: Sample list of stock tickers.
    """
    return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META']

@pytest.fixture
def sample_config():
    """
    Create a sample configuration dictionary for testing.

    Returns:
        dict: Sample configuration.
    """
    return {
        'data': {
            'start_date': '2022-01-01',
            'end_date': '2023-01-01',
            'interval': '1d'
        },
        'surge': {
            'min_surge_percent': 20,
            'max_drawdown_percent': 10,
            'min_surge_days': 5,
            'max_surge_days': 60
        },
        'output': {
            'results_dir': 'results/surge_analyzer'
        }
    }

@pytest.fixture
def mock_stock_data_fetcher(monkeypatch, sample_stock_data):
    """
    Create a mock for the StockDataFetcher class.

    Args:
        monkeypatch: pytest's monkeypatch fixture
        sample_stock_data: the sample_stock_data fixture

    Returns:
        function: A function that returns a mocked StockDataFetcher
    """
    class MockStockDataFetcher:
        def __init__(self, *args, **kwargs):
            pass

        def fetch_data(self, ticker, start_date=None, end_date=None, interval='1d'):
            return sample_stock_data

    def _get_mock_fetcher(*args, **kwargs):
        return MockStockDataFetcher(*args, **kwargs)

    monkeypatch.setattr('src.surge_analyzer.data.data_fetcher.StockDataFetcher', _get_mock_fetcher)
    return _get_mock_fetcher

@pytest.fixture
def mock_stock_list_fetcher(monkeypatch, sample_stock_list):
    """
    Create a mock for the StockListFetcher class.

    Args:
        monkeypatch: pytest's monkeypatch fixture
        sample_stock_list: the sample_stock_list fixture

    Returns:
        function: A function that returns a mocked StockListFetcher
    """
    class MockStockListFetcher:
        def __init__(self, *args, **kwargs):
            pass

        def fetch_stock_list(self, list_name=None):
            return sample_stock_list

    def _get_mock_fetcher(*args, **kwargs):
        return MockStockListFetcher(*args, **kwargs)

    monkeypatch.setattr('src.surge_analyzer.data.stock_list_fetcher.StockListFetcher', _get_mock_fetcher)
    return _get_mock_fetcher
