#!/usr/bin/env python3
"""
Stock Finder Pattern Recognition

Main entry point for the application. This script can run:
1. The pipeline only (to analyze stocks and generate results)
2. The dashboard only (to visualize existing results)
3. Both the pipeline and dashboard in sequence

Usage:
    python bin/surge_analyzer/run_surge_analyzer.py --mode pipeline --config config/surge_analyzer/surge_analyzer_config.yaml
    python bin/surge_analyzer/run_surge_analyzer.py --mode dashboard --results results/surge_analyzer/surge_analyzer_analysis.json
    python bin/surge_analyzer/run_surge_analyzer.py --mode both --tickers AAPL,MSFT,GOOGL --years 3 --min-surge 50
"""

import os
import sys
import argparse
import pandas as pd
import logging
import datetime
from pathlib import Path

# Add the project root directory to the path so we can import from src
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.surge_analyzer.utils.config_loader import load_config
from src.surge_analyzer.data.data_fetcher import StockDataFetcher
from src.surge_analyzer.analysis.surge_analyzer import SurgeAnalyzer
from src.surge_analyzer.data.stock_list_fetcher import StockListFetcher
from src.surge_analyzer.dashboard import run_dashboard


# Helper function to convert values to scalar
def to_scalar(value):
    if hasattr(value, 'item'):
        return value.item()
    elif isinstance(value, (list, pd.Series)):
        if len(value) > 0:
            first_val = value[0]
            if hasattr(first_val, 'item'):
                return first_val.item()
            return float(first_val)
        return 0.0
    return value


def run_pipeline(config):
    """
    Run the stock finder pipeline.

    Args:
        config: Configuration dictionary

    Returns:
        Tuple of (success, output_path)
    """
    # Extract configuration values
    years_back = config.get('analysis', {}).get('years_back', 5)
    min_surge_percent = config.get('analysis', {}).get('min_surge_percent', 100.0)
    price_col = config.get('analysis', {}).get('price_col', 'Close')
    batch_size = config.get('analysis', {}).get('batch_size', 50)
    data_dir = config.get('data', {}).get('data_dir', 'data/surge_analyzer')
    interval = config.get('data', {}).get('interval', '1d')
    output_format = config.get('output', {}).get('format', 'console')

    # Set up results directory and output path
    results_dir = config.get('output', {}).get('results_dir', 'results/surge_analyzer')
    os.makedirs(results_dir, exist_ok=True)

    if output_format == 'csv':
        output_path = os.path.join(results_dir, config.get('output', {}).get('csv_path', 'surge_analysis.csv'))
    else:
        output_path = None

    # Get tickers from configuration
    stock_selection = config.get('stock_selection', {})
    source = stock_selection.get('source', 'manual')
    max_stocks = stock_selection.get('max_stocks', 0)
    use_cache = stock_selection.get('use_cache', True)

    # If source is manual, get tickers from the tickers parameter
    if source == 'manual':
        tickers_str = config.get('tickers', '')
        if not tickers_str:
            error_msg = "Error: No stock tickers specified for manual source."
            print(error_msg)
            logging.error(error_msg)
            return False, None

        tickers = [ticker.strip().upper() for ticker in tickers_str.split(',')]
    else:
        # Use the stock list fetcher for other sources
        stock_lists_cache_dir = config.get('data', {}).get('stock_lists_cache_dir', 'data/surge_analyzer/stock_lists')
        fetcher = StockListFetcher(cache_dir=stock_lists_cache_dir)

        # Prepare kwargs for the fetcher
        kwargs = {'use_cache': use_cache}

        if source == 'custom':
            kwargs['file_path'] = stock_selection.get('custom_file_path', '')
        elif source == 'market_cap':
            kwargs['min_cap'] = stock_selection.get('min_market_cap', 1e9)
            kwargs['max_cap'] = stock_selection.get('max_market_cap')
            kwargs['limit'] = max_stocks if max_stocks > 0 else 100
        elif source == 'sector':
            kwargs['sector'] = stock_selection.get('sector', '')

        # Get tickers from the specified source
        tickers = fetcher.get_tickers_by_source(source, **kwargs)

    # Limit the number of tickers if max_stocks is specified
    if max_stocks > 0 and len(tickers) > max_stocks:
        print(f"Limiting analysis to {max_stocks} stocks out of {len(tickers)} available")
        tickers = tickers[:max_stocks]

    print(f"Analyzing {len(tickers)} stocks over {years_back} years...")
    print(f"Looking for price surges of at least {min_surge_percent}%")

    # Create data directory if it doesn't exist
    os.makedirs(data_dir, exist_ok=True)

    # Initialize fetcher and analyzer
    data_fetcher = StockDataFetcher(data_dir=data_dir)
    surge_analyzer = SurgeAnalyzer()

    # Process tickers in batches
    all_surge_stocks = {}
    all_detailed_analysis = {}

    for i in range(0, len(tickers), batch_size):
        batch = tickers[i:i+batch_size]
        print(f"Processing batch {i//batch_size + 1}/{(len(tickers)-1)//batch_size + 1} ({len(batch)} stocks)...")

        # Fetch data for the batch
        stock_data_dict = data_fetcher.fetch_multiple_stocks(batch, years_back, interval)

        # Analyze surges
        surge_stocks = surge_analyzer.find_surge_stocks(stock_data_dict, min_surge_percent, price_col)

        # Perform detailed analysis on surge stocks
        detailed_analysis = {}
        for ticker in surge_stocks.keys():
            detailed_analysis[ticker] = surge_analyzer.analyze_surge_patterns(stock_data_dict[ticker], price_col)

        # Merge results
        all_surge_stocks.update(surge_stocks)
        all_detailed_analysis.update(detailed_analysis)

    # Display results
    if not all_surge_stocks:
        print("No stocks found with the specified surge criteria.")
    else:
        print("\n=== STOCKS WITH SIGNIFICANT PRICE SURGES ===\n")
        print(f"{'Ticker':<10} {'Total Change %':<15} {'Annualized Return %':<20} {'Volatility %':<15} {'Max Drawdown %':<15}")
        print("-" * 80)

        # Use the to_scalar function defined at the module level

        for ticker, percent_change in sorted(all_surge_stocks.items(), key=lambda x: x[1], reverse=True):
            analysis = all_detailed_analysis.get(ticker, {})

            # Convert values to scalar for formatting
            annualized_return = to_scalar(analysis.get('annualized_return_percent', 'N/A'))
            volatility = to_scalar(analysis.get('volatility_percent', 'N/A'))
            max_drawdown = to_scalar(analysis.get('max_drawdown_percent', 'N/A'))

            # Format the values for display
            if isinstance(annualized_return, (int, float)):
                annualized_return_str = f"{annualized_return:.2f}"
            else:
                annualized_return_str = str(annualized_return)

            if isinstance(volatility, (int, float)):
                volatility_str = f"{volatility:.2f}"
            else:
                volatility_str = str(volatility)

            if isinstance(max_drawdown, (int, float)):
                max_drawdown_str = f"{max_drawdown:.2f}"
            else:
                max_drawdown_str = str(max_drawdown)

            print(f"{ticker:<10} {percent_change:<15.2f} {annualized_return_str:<20} {volatility_str:<15} {max_drawdown_str:<15}")

    # Save results to CSV if requested
    if output_format == 'csv' and all_surge_stocks:
        # Create a DataFrame from the detailed analysis
        rows = []
        for ticker, percent_change in all_surge_stocks.items():
            analysis = all_detailed_analysis.get(ticker, {})
            if not analysis:
                continue

            # Create a row with the analysis data
            row = {
                'Ticker': ticker,
                'Total Change %': float(percent_change),
                'Annualized Return %': float(to_scalar(analysis.get('annualized_return_percent', 0.0))),
                'Start Date': str(analysis.get('start_date', '')),
                'End Date': str(analysis.get('end_date', '')),
                'Days Analyzed': int(to_scalar(analysis.get('days_analyzed', 0))),
                'Start Price': float(to_scalar(analysis.get('start_price', 0.0))),
                'End Price': float(to_scalar(analysis.get('end_price', 0.0))),
                'Volatility %': float(to_scalar(analysis.get('volatility_percent', 0.0))),
                'Max Drawdown %': float(to_scalar(analysis.get('max_drawdown_percent', 0.0))),
                'Drawdown Peak Date': str(analysis.get('drawdown_peak_date', '')),
                'Drawdown Trough Date': str(analysis.get('drawdown_trough_date', '')),
                'Surge Start Date': str(analysis.get('surge_start_date', '')),
                'Surge End Date': str(analysis.get('surge_end_date', '')),
                'Surge Start Price': float(to_scalar(analysis.get('surge_start_price', 0.0))),
                'Surge End Price': float(to_scalar(analysis.get('surge_end_price', 0.0))),
                'Surge Percent Change': float(to_scalar(analysis.get('surge_percent_change', 0.0)))
            }
            rows.append(row)

        # Create and save the DataFrame
        import pandas as pd
        df = pd.DataFrame(rows)

        # Ensure the results directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        df.to_csv(output_path, index=False)
        print(f"\nResults saved to {output_path}")

        # Also save a JSON version for the dashboard
        json_path = output_path.replace('.csv', '.json')
        df.to_json(json_path, orient='records')
        print(f"JSON results saved to {json_path}")

        return True, json_path

    print(f"\nAnalysis complete. Found {len(all_surge_stocks)} stocks with significant price surges.")
    return True, output_path


def setup_logging(disable_file_logging=False):
    """Set up logging for the pipeline run.

    Args:
        disable_file_logging (bool): If True, only log to stdout, not to a file.
                                    This is useful when the shell script is already
                                    capturing output to a log file.
    """
    # Create logs directory if it doesn't exist
    os.makedirs('logs/surge_analyzer', exist_ok=True)

    # Create a timestamp for the log file
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f'logs/surge_analyzer/pipeline_run_{timestamp}.log'

    # Configure logging handlers
    handlers = [logging.StreamHandler(sys.stdout)]

    # Add file handler only if file logging is not disabled
    if not disable_file_logging:
        handlers.append(logging.FileHandler(log_file))

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=handlers
    )

    # Redirect stdout and stderr to the log file as well
    class LoggerWriter:
        def __init__(self, logger, level):
            self.logger = logger
            self.level = level
            self.buffer = ''

        def write(self, message):
            if message and message.strip():
                self.logger.log(self.level, message.strip())
            return len(message)

        def flush(self):
            pass

    # Create a logger for stdout and stderr
    stdout_logger = logging.getLogger('STDOUT')
    stderr_logger = logging.getLogger('STDERR')

    # Save the original stdout and stderr
    sys.stdout_original = sys.stdout
    sys.stderr_original = sys.stderr

    # Redirect stdout and stderr to the logger
    sys.stdout = LoggerWriter(stdout_logger, logging.INFO)
    sys.stderr = LoggerWriter(stderr_logger, logging.ERROR)

    logging.info(f"Starting surge analyzer pipeline at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Log file: {log_file}")

    return log_file

def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description='Stock Finder Pattern Recognition')

    # Add a flag to disable file logging
    parser.add_argument('--disable-file-logging', action='store_true',
                        help='Disable logging to a file (useful when the shell script captures output)')

    # Mode selection
    parser.add_argument('--mode', '-m', type=str, choices=['pipeline', 'dashboard', 'both'], default='both',
                        help='Mode to run: pipeline, dashboard, or both')

    # Pipeline options
    parser.add_argument('--config', '-c', type=str, default='config/surge_analyzer/surge_analyzer_config.yaml',
                        help='Path to configuration file')
    parser.add_argument('--preset', '-pr', type=str,
                        help='Configuration preset to use (default, conservative, aggressive, sp500, sec_filers)')
    parser.add_argument('--tickers', '-t', type=str,
                        help='Comma-separated list of stock tickers to analyze')
    parser.add_argument('--years', '-y', type=int,
                        help='Number of years to look back')
    parser.add_argument('--min-surge', '-s', type=float,
                        help='Minimum percentage surge to identify')
    parser.add_argument('--output', '-o', type=str,
                        help='Output CSV file path')

    # Dashboard options
    parser.add_argument('--results', '-r', type=str,
                        help='Path to results file for dashboard')
    parser.add_argument('--port', '-p', type=int, default=8050,
                        help='Port to run the dashboard on')
    parser.add_argument('--debug', '-d', action='store_true',
                        help='Run dashboard in debug mode')

    # Parse all arguments
    args = parser.parse_args()

    # Set up logging with the disable_file_logging flag
    setup_logging(disable_file_logging=args.disable_file_logging)

    # Run in the selected mode
    if args.mode in ['pipeline', 'both']:
        # Load configuration
        cmd_args = vars(args)
        config = load_config(config_path=args.config, preset=args.preset, cmd_args=cmd_args)

        # Configuration is now fully loaded with command-line arguments applied
        logging.info(f"Configuration loaded from {args.config}")
        if args.preset:
            logging.info(f"Using preset: {args.preset}")

        # Log key configuration parameters
        logging.info(f"Analysis parameters: years_back={config.get('analysis', {}).get('years_back', 5)}, "
                    f"min_surge_percent={config.get('analysis', {}).get('min_surge_percent', 100.0)}")

        source = config.get('stock_selection', {}).get('source', 'manual')
        logging.info(f"Stock selection source: {source}")

        if source == 'manual':
            tickers_str = config.get('tickers', '')
            logging.info(f"Manual tickers: {tickers_str}")

        # Run the pipeline
        logging.info("Starting pipeline execution")
        success, results_path = run_pipeline(config)

        if not success:
            logging.error("Pipeline failed.")
            # Restore original stdout and stderr before exiting
            if hasattr(sys, 'stdout_original'):
                sys.stdout = sys.stdout_original
            if hasattr(sys, 'stderr_original'):
                sys.stderr = sys.stderr_original
            return 1

        logging.info(f"Pipeline completed successfully. Results path: {results_path}")

        # If only running the pipeline, exit here
        if args.mode == 'pipeline':
            logging.info("Pipeline mode only. Exiting.")
            return 0

    # Run the dashboard
    if args.mode in ['dashboard', 'both']:
        logging.info("Preparing to run dashboard")

        # Determine the results path
        if args.mode == 'both' and 'results_path' in locals():
            dashboard_results = results_path
            logging.info(f"Using results from pipeline run: {dashboard_results}")
        elif args.results:
            dashboard_results = args.results
            logging.info(f"Using specified results file: {dashboard_results}")
        else:
            # Find the latest results file
            import glob
            result_files = glob.glob('results/surge_analyzer/*.json')
            if not result_files:
                logging.error("No results files found. Please run the pipeline first or specify a results file.")
                # Restore original stdout and stderr before exiting
                if hasattr(sys, 'stdout_original'):
                    sys.stdout = sys.stdout_original
                if hasattr(sys, 'stderr_original'):
                    sys.stderr = sys.stderr_original
                return 1

            # Get the most recent file
            dashboard_results = max(result_files, key=os.path.getmtime)
            logging.info(f"Using most recent results file: {dashboard_results}")

        logging.info(f"Starting dashboard with results from {dashboard_results} on port {args.port}")

        # Run the dashboard
        try:
            run_dashboard(results_path=dashboard_results, debug=args.debug, port=args.port)
        except KeyboardInterrupt:
            logging.info("Dashboard stopped by user.")
        except Exception as e:
            logging.error(f"Error running dashboard: {e}")
            # Restore original stdout and stderr before exiting
            if hasattr(sys, 'stdout_original'):
                sys.stdout = sys.stdout_original
            if hasattr(sys, 'stderr_original'):
                sys.stderr = sys.stderr_original
            return 1

    logging.info("Application completed successfully")

    # Restore original stdout and stderr
    if hasattr(sys, 'stdout_original'):
        sys.stdout = sys.stdout_original
    if hasattr(sys, 'stderr_original'):
        sys.stderr = sys.stderr_original

    return 0


if __name__ == "__main__":
    sys.exit(main())
