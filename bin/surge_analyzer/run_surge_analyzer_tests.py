#!/usr/bin/env python3
"""
Run tests for the Surge Analyzer.

This script runs the tests for the Surge Analyzer using pytest.
It is the main entry point for running tests in the project.
"""

import os
import sys
import argparse
import subprocess
import logging
import datetime


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run tests for the Surge Analyzer')

    parser.add_argument(
        '--unit',
        action='store_true',
        help='Run unit tests only'
    )

    parser.add_argument(
        '--functional',
        action='store_true',
        help='Run functional tests only'
    )

    parser.add_argument(
        '--coverage',
        action='store_true',
        help='Generate coverage report'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Verbose output'
    )

    parser.add_argument(
        '--html',
        action='store_true',
        help='Generate HTML report'
    )

    parser.add_argument(
        '--module',
        type=str,
        help='Run tests for a specific module (e.g., analysis, data, technical_analysis, utils)'
    )

    return parser.parse_args()


def setup_logging():
    """Set up logging for the test run."""
    # Create logs directory if it doesn't exist
    os.makedirs('logs/surge_analyzer', exist_ok=True)

    # Create a timestamp for the log file
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f'logs/surge_analyzer/test_run_{timestamp}.log'

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logging.info(f"Starting test run at {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logging.info(f"Log file: {log_file}")

    return log_file

def run_tests(args):
    """Run the tests based on the command line arguments."""
    # Set up logging
    log_file = setup_logging()

    # Construct the pytest command
    cmd = ['pytest']

    # Add verbosity
    if args.verbose:
        cmd.append('-v')

    # Add coverage
    if args.coverage:
        cmd.extend(['--cov=src/surge_analyzer', '--cov-report=term'])
        if args.html:
            cmd.append('--cov-report=html')

    # Add HTML report
    if args.html and not args.coverage:
        cmd.append('--html=report.html')

    # Add test selection
    if args.unit:
        cmd.append('tests/surge_analyzer/unit')
    elif args.functional:
        cmd.append('tests/surge_analyzer/functional')
    else:
        cmd.append('tests/surge_analyzer')

    # Add module selection
    if args.module:
        if args.unit:
            cmd[-1] = f'tests/surge_analyzer/unit/{args.module}'
        elif args.functional:
            cmd[-1] = f'tests/surge_analyzer/functional/{args.module}'
        else:
            cmd[-1] = f'tests/surge_analyzer/unit/{args.module}'

    # Add output logging to file
    cmd.extend(['-v', f'--log-file={log_file}'])

    # Run the tests
    logging.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)

    # Log the output
    logging.info("Test output:")
    logging.info(result.stdout)

    if result.stderr:
        logging.error("Test errors:")
        logging.error(result.stderr)

    # Log the result
    if result.returncode == 0:
        logging.info("Tests passed successfully")
    else:
        logging.error(f"Tests failed with return code {result.returncode}")

    return result.returncode


def main():
    """Main entry point."""
    args = parse_args()
    return run_tests(args)


if __name__ == '__main__':
    sys.exit(main())
