# Surge Analyzer

A powerful tool for identifying and analyzing stocks with significant price surges.

## Overview

The Surge Analyzer is a comprehensive pipeline designed to help investors and traders identify stocks that have experienced substantial price increases over a specified time period. By analyzing historical price data, the tool identifies stocks with price surges exceeding a configurable threshold and provides detailed metrics to help evaluate these opportunities.

## Quick Start

### For Linux/Mac Users:

```bash
# Run both the pipeline and dashboard
./scripts/run_surge_analyzer.sh

# Run only the dashboard
./scripts/run_surge_analyzer.sh --dashboard

# Run only the pipeline
./scripts/run_surge_analyzer.sh --pipeline
```

### For Windows Users:

```
# Run both the pipeline and dashboard
scripts\run_surge_analyzer.bat

# Run only the dashboard
scripts\run_surge_analyzer.bat --dashboard

# Run only the pipeline
scripts\run_surge_analyzer.bat --pipeline
```

## Features

- **Automated Stock List Acquisition**: Fetch stock lists from various sources (S&P 500, NASDAQ 100, NYSE, etc.)
- **Historical Data Analysis**: Analyze years of price data to identify significant surge patterns
- **Technical Analysis**: Calculate key technical indicators (moving averages, RSI, MACD, etc.)
- **Detailed Metrics**: Get comprehensive statistics including annualized returns, volatility, and drawdowns
- **Interactive Dashboard**: Explore results through an intuitive web interface with interactive charts
- **Flexible Configuration**: Customize analysis parameters through configuration files or command-line options

## Documentation

For detailed documentation, please refer to the following:

- [Project Overview](documentation/surge_analyzer/README.md): Standard project overview with installation instructions and basic usage examples
- [Architecture Overview](documentation/surge_analyzer/AUGMENT_SUMMARY.md): Detailed description of the system architecture, components, and current status
- [Development Guide](documentation/surge_analyzer/DEVELOPMENT.md): Information for developers, including code organization and testing procedures
- [API Reference](documentation/surge_analyzer/API_REFERENCE.md): Detailed documentation of classes and methods
- [Configuration Guide](documentation/surge_analyzer/CONFIGURATION.md): Comprehensive guide to configuration options

## Project Structure

```
surge-analyzer/
├── bin/                    # Executable Python scripts
├── config/                 # Configuration files
├── data/                   # Data storage
│   └── surge_analyzer/     # Data for surge analyzer
├── documentation/          # Documentation files
│   └── surge_analyzer/     # Surge analyzer documentation
├── logs/                   # Log files
│   └── surge_analyzer/     # Surge analyzer logs
├── requirements/           # Dependency specifications
├── results/                # Analysis results
│   └── surge_analyzer/     # Results from surge analyzer
├── scripts/                # Shell scripts
├── src/                    # Source code
│   └── surge_analyzer/     # Surge analyzer code
├── tests/                  # Test suite
│   └── surge_analyzer/     # Tests for surge analyzer
└── venvs/                  # Virtual environments
    └── surge_analyzer/     # Surge analyzer virtual environment
```

## Requirements

- Python 3.8 or higher
- pip (Python package installer)
- Internet connection to download dependencies and stock data

## License

This project is licensed under the MIT License - see the LICENSE file for details.
