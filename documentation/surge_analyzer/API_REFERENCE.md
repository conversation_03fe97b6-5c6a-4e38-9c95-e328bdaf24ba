# Surge Analyzer API Reference

This document provides detailed documentation for all public classes, methods, and functions in the Surge Analyzer pipeline.

## Table of Contents

- [Data Module](#data-module)
  - [StockDataFetcher](#stockdatafetcher)
  - [StockListFetcher](#stocklistfetcher)
- [Analysis Module](#analysis-module)
  - [SurgeAnalyzer](#surgeanalyzer)
- [Technical Analysis Module](#technical-analysis-module)
  - [TechnicalIndicators](#technicalindicators)
- [Utils Module](#utils-module)
  - [ConfigLoader](#configloader)
- [Dashboard Module](#dashboard-module)
  - [Dashboard Functions](#dashboard-functions)

## Data Module

The data module provides classes for fetching stock lists and historical price data.

### StockDataFetcher

`StockDataFetcher` is responsible for retrieving historical stock data from Yahoo Finance.

#### Constructor

```python
StockDataFetcher(data_dir: str = "./data")
```

**Parameters:**
- `data_dir` (str): Directory to store downloaded data

#### Methods

##### fetch_stock_data

```python
fetch_stock_data(
    ticker: str,
    years_back: int = 5,
    interval: str = "1d",
    max_retries: int = 3
) -> pd.DataFrame
```

**Parameters:**
- `ticker` (str): Stock ticker symbol
- `years_back` (int): Number of years of historical data to fetch
- `interval` (str): Data interval (1d, 1wk, 1mo, etc.)
- `max_retries` (int): Maximum number of retry attempts

**Returns:**
- `pd.DataFrame`: DataFrame containing historical stock data with columns:
  - Open: Opening price
  - High: Highest price during the period
  - Low: Lowest price during the period
  - Close: Closing price
  - Adj Close: Adjusted closing price
  - Volume: Trading volume

**Exceptions:**
- `Exception`: If data fetching fails after max_retries

**Example:**
```python
fetcher = StockDataFetcher(data_dir="data/surge_analyzer")
aapl_data = fetcher.fetch_stock_data("AAPL", years_back=3, interval="1d")
print(f"Fetched {len(aapl_data)} days of data for AAPL")
```

##### fetch_multiple_stocks

```python
fetch_multiple_stocks(
    tickers: List[str],
    years_back: int = 5,
    interval: str = "1d"
) -> Dict[str, pd.DataFrame]
```

**Parameters:**
- `tickers` (List[str]): List of stock ticker symbols
- `years_back` (int): Number of years of historical data to fetch
- `interval` (str): Data interval (1d, 1wk, 1mo, etc.)

**Returns:**
- `Dict[str, pd.DataFrame]`: Dictionary mapping tickers to their historical data DataFrames

**Example:**
```python
fetcher = StockDataFetcher(data_dir="data/surge_analyzer")
tickers = ["AAPL", "MSFT", "GOOGL"]
data_dict = fetcher.fetch_multiple_stocks(tickers, years_back=2)
for ticker, data in data_dict.items():
    print(f"{ticker}: {len(data)} days of data")
```

### StockListFetcher

`StockListFetcher` is responsible for obtaining lists of stock tickers from various sources.

#### Constructor

```python
StockListFetcher(cache_dir: str = "data/surge_analyzer/stock_lists")
```

**Parameters:**
- `cache_dir` (str): Directory to cache downloaded stock lists

#### Methods

##### fetch_sp500_tickers

```python
fetch_sp500_tickers(use_cache: bool = True) -> List[str]
```

**Parameters:**
- `use_cache` (bool): Whether to use cached data if available

**Returns:**
- `List[str]`: List of S&P 500 tickers

**Example:**
```python
fetcher = StockListFetcher()
sp500_tickers = fetcher.fetch_sp500_tickers()
print(f"Fetched {len(sp500_tickers)} S&P 500 tickers")
```

##### fetch_nasdaq100_tickers

```python
fetch_nasdaq100_tickers(use_cache: bool = True) -> List[str]
```

**Parameters:**
- `use_cache` (bool): Whether to use cached data if available

**Returns:**
- `List[str]`: List of NASDAQ 100 tickers

##### fetch_dow_jones_tickers

```python
fetch_dow_jones_tickers(use_cache: bool = True) -> List[str]
```

**Parameters:**
- `use_cache` (bool): Whether to use cached data if available

**Returns:**
- `List[str]`: List of Dow Jones Industrial Average tickers

##### fetch_nyse_tickers

```python
fetch_nyse_tickers(use_cache: bool = True) -> List[str]
```

**Parameters:**
- `use_cache` (bool): Whether to use cached data if available

**Returns:**
- `List[str]`: List of NYSE tickers

##### fetch_custom_list

```python
fetch_custom_list(file_path: str) -> List[str]
```

**Parameters:**
- `file_path` (str): Path to the file containing tickers (one per line or CSV)

**Returns:**
- `List[str]`: List of tickers from the file

##### fetch_tickers_by_market_cap

```python
fetch_tickers_by_market_cap(
    min_cap: float = 1e9,
    max_cap: Optional[float] = None,
    limit: int = 100,
    use_cache: bool = True
) -> List[str]
```

**Parameters:**
- `min_cap` (float): Minimum market cap in USD
- `max_cap` (Optional[float]): Maximum market cap in USD (optional)
- `limit` (int): Maximum number of tickers to return
- `use_cache` (bool): Whether to use cached data if available

**Returns:**
- `List[str]`: List of tickers filtered by market cap

##### fetch_tickers_by_sector

```python
fetch_tickers_by_sector(
    sector: str,
    use_cache: bool = True
) -> List[str]
```

**Parameters:**
- `sector` (str): Sector name (e.g., 'Technology', 'Healthcare')
- `use_cache` (bool): Whether to use cached data if available

**Returns:**
- `List[str]`: List of tickers in the specified sector

##### get_tickers_by_source

```python
get_tickers_by_source(
    source: str,
    **kwargs
) -> List[str]
```

**Parameters:**
- `source` (str): Source name ('sp500', 'nasdaq100', 'dow_jones', 'nyse', 'custom', 'market_cap', 'sector', 'all_us', 'sec_filers')
- `**kwargs`: Additional arguments for the specific source

**Returns:**
- `List[str]`: List of tickers from the specified source

**Example:**
```python
fetcher = StockListFetcher()
# Get S&P 500 tickers
sp500_tickers = fetcher.get_tickers_by_source('sp500')
# Get technology sector tickers
tech_tickers = fetcher.get_tickers_by_source('sector', sector='Technology')
# Get large-cap tickers
large_cap_tickers = fetcher.get_tickers_by_source('market_cap', min_cap=10e9, limit=50)
```

## Analysis Module

The analysis module provides classes for analyzing stock data to identify surge patterns.

### SurgeAnalyzer

`SurgeAnalyzer` is responsible for identifying and analyzing price surges in stock data.

#### Constructor

```python
SurgeAnalyzer()
```

#### Methods

##### find_surge_stocks

```python
find_surge_stocks(
    stock_data_dict: Dict[str, pd.DataFrame],
    min_surge_percent: float,
    price_col: str = "Close"
) -> Dict[str, float]
```

**Parameters:**
- `stock_data_dict` (Dict[str, pd.DataFrame]): Dictionary mapping tickers to their historical data
- `min_surge_percent` (float): Minimum percentage surge to identify
- `price_col` (str): Price column to use for analysis

**Returns:**
- `Dict[str, float]`: Dictionary mapping tickers to their percentage change

**Example:**
```python
analyzer = SurgeAnalyzer()
data_fetcher = StockDataFetcher()
tickers = ["AAPL", "MSFT", "GOOGL"]
data_dict = data_fetcher.fetch_multiple_stocks(tickers, years_back=2)
surge_stocks = analyzer.find_surge_stocks(data_dict, min_surge_percent=50.0)
for ticker, percent_change in surge_stocks.items():
    print(f"{ticker}: {percent_change:.2f}%")
```

##### analyze_surge_patterns

```python
analyze_surge_patterns(
    stock_data: pd.DataFrame,
    price_col: str = "Close"
) -> Dict[str, Any]
```

**Parameters:**
- `stock_data` (pd.DataFrame): Historical stock data
- `price_col` (str): Price column to use for analysis

**Returns:**
- `Dict[str, Any]`: Dictionary containing analysis results:
  - `start_date`: Start date of the analysis period
  - `end_date`: End date of the analysis period
  - `days_analyzed`: Number of days analyzed
  - `start_price`: Price at the start of the period
  - `end_price`: Price at the end of the period
  - `percent_change`: Total percentage change
  - `annualized_return_percent`: Annualized return percentage
  - `volatility_percent`: Volatility as a percentage
  - `max_drawdown_percent`: Maximum drawdown as a percentage
  - `drawdown_peak_date`: Date of the drawdown peak
  - `drawdown_trough_date`: Date of the drawdown trough
  - `surge_start_date`: Start date of the best surge period
  - `surge_end_date`: End date of the best surge period
  - `surge_start_price`: Price at the start of the best surge
  - `surge_end_price`: Price at the end of the best surge
  - `surge_percent_change`: Percentage change during the best surge

## Technical Analysis Module

The technical analysis module provides functions for calculating technical indicators.

### TechnicalIndicators

`TechnicalIndicators` provides static methods for calculating various technical indicators.

#### Methods

##### calculate_moving_averages

```python
@staticmethod
calculate_moving_averages(
    data: pd.DataFrame,
    price_col: str = "Close",
    windows: List[int] = [20, 50, 200]
) -> pd.DataFrame
```

**Parameters:**
- `data` (pd.DataFrame): Historical stock data
- `price_col` (str): Price column to use
- `windows` (List[int]): List of window sizes for moving averages

**Returns:**
- `pd.DataFrame`: Original data with additional columns for each moving average

**Example:**
```python
data = data_fetcher.fetch_stock_data("AAPL", years_back=1)
data_with_mas = TechnicalIndicators.calculate_moving_averages(data)
print(data_with_mas.tail())
```

##### calculate_rsi

```python
@staticmethod
calculate_rsi(
    data: pd.DataFrame,
    price_col: str = "Close",
    window: int = 14
) -> pd.DataFrame
```

**Parameters:**
- `data` (pd.DataFrame): Historical stock data
- `price_col` (str): Price column to use
- `window` (int): Window size for RSI calculation

**Returns:**
- `pd.DataFrame`: Original data with an additional 'RSI' column

##### calculate_macd

```python
@staticmethod
calculate_macd(
    data: pd.DataFrame,
    price_col: str = "Close",
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9
) -> pd.DataFrame
```

**Parameters:**
- `data` (pd.DataFrame): Historical stock data
- `price_col` (str): Price column to use
- `fast_period` (int): Fast period for MACD calculation
- `slow_period` (int): Slow period for MACD calculation
- `signal_period` (int): Signal period for MACD calculation

**Returns:**
- `pd.DataFrame`: Original data with additional 'MACD', 'MACD_Signal', and 'MACD_Histogram' columns

##### calculate_bollinger_bands

```python
@staticmethod
calculate_bollinger_bands(
    data: pd.DataFrame,
    price_col: str = "Close",
    window: int = 20,
    num_std: float = 2.0
) -> pd.DataFrame
```

**Parameters:**
- `data` (pd.DataFrame): Historical stock data
- `price_col` (str): Price column to use
- `window` (int): Window size for moving average
- `num_std` (float): Number of standard deviations for the bands

**Returns:**
- `pd.DataFrame`: Original data with additional 'BB_Middle', 'BB_Upper', and 'BB_Lower' columns

## Utils Module

The utils module provides utility functions for the Surge Analyzer pipeline.

### ConfigLoader

`ConfigLoader` provides functions for loading and managing configuration.

#### Functions

##### load_config

```python
load_config(
    config_path: str = "config/surge_analyzer_config.yaml",
    preset: Optional[str] = None,
    cmd_args: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]
```

**Parameters:**
- `config_path` (str): Path to the configuration file
- `preset` (Optional[str]): Configuration preset to use
- `cmd_args` (Optional[Dict[str, Any]]): Command-line arguments to override configuration

**Returns:**
- `Dict[str, Any]`: Merged configuration dictionary

**Example:**
```python
config = load_config(
    config_path="config/surge_analyzer_config.yaml",
    preset="aggressive",
    cmd_args={"years": 3, "min_surge": 75.0}
)
print(f"Analysis parameters: years_back={config['analysis']['years_back']}, "
      f"min_surge_percent={config['analysis']['min_surge_percent']}")
```

## Dashboard Module

The dashboard module provides functions for creating and running the interactive dashboard.

### Dashboard Functions

#### run_dashboard

```python
run_dashboard(
    results_path: Optional[str] = None,
    debug: bool = False,
    port: int = 8050
) -> None
```

**Parameters:**
- `results_path` (Optional[str]): Path to the results file (JSON)
- `debug` (bool): Whether to run in debug mode
- `port` (int): Port to run the dashboard on

**Example:**
```python
from src.surge_analyzer.dashboard import run_dashboard

# Run the dashboard with the latest results
run_dashboard(debug=True, port=8080)
```

#### create_dashboard

```python
create_dashboard(
    results_path: str
) -> dash.Dash
```

**Parameters:**
- `results_path` (str): Path to the results file (JSON)

**Returns:**
- `dash.Dash`: Dash application instance
