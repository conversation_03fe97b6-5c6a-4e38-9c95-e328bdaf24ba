# Surge Analyzer Configuration Guide

This document provides a comprehensive guide to configuring the Surge Analyzer pipeline. The configuration system is designed to be flexible, allowing you to customize the behavior of the pipeline to suit your specific needs.

## Configuration File

The Surge Analyzer uses a YAML configuration file located at `config/surge_analyzer/surge_analyzer_config.yaml`. This file contains all the settings for the pipeline, organized into logical sections.

### File Structure

The configuration file is structured as follows:

```yaml
# Data settings
data:
  data_dir: "data/surge_analyzer"
  stock_lists_cache_dir: "data/surge_analyzer/stock_lists"
  interval: "1d"

# Analysis settings
analysis:
  years_back: 5
  min_surge_percent: 50.0
  price_col: "Close"
  batch_size: 50

  # Presets for different analysis strategies
  presets:
    # Default preset (same as top-level settings)
    default:
      years_back: 5
      min_surge_percent: 50.0

    # Conservative preset (lower surge threshold)
    conservative:
      years_back: 5
      min_surge_percent: 30.0

    # Aggressive preset (higher surge threshold)
    aggressive:
      years_back: 5
      min_surge_percent: 100.0

# Stock selection settings
stock_selection:
  source: "sp500"
  max_stocks: 500
  use_cache: true
  custom_file_path: ""
  min_market_cap: 1000000000
  max_market_cap: null
  sector: ""

# Output settings
output:
  results_dir: "results/surge_analyzer"
  format: "csv"
  csv_path: "surge_analysis.csv"

# Dashboard settings
dashboard:
  port: 8050
  debug: false
  theme: "light"
```

## Configuration Sections

### Data Settings

The `data` section controls how and where data is fetched and stored.

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `data_dir` | string | `"data/surge_analyzer"` | Directory for storing stock data |
| `stock_lists_cache_dir` | string | `"data/surge_analyzer/stock_lists"` | Directory for caching stock lists |
| `interval` | string | `"1d"` | Data interval (1d, 1wk, 1mo) |

**Example:**
```yaml
data:
  data_dir: "data/surge_analyzer"
  stock_lists_cache_dir: "data/surge_analyzer/stock_lists"
  interval: "1d"
```

### Analysis Settings

The `analysis` section controls the parameters used for surge analysis.

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `years_back` | integer | `5` | Number of years to look back |
| `min_surge_percent` | float | `50.0` | Minimum percentage surge to identify |
| `price_col` | string | `"Close"` | Price column to use for analysis |
| `batch_size` | integer | `50` | Batch size for processing stocks |

**Example:**
```yaml
analysis:
  years_back: 3
  min_surge_percent: 75.0
  price_col: "Close"
  batch_size: 100
```

#### Analysis Presets

The `analysis.presets` section defines presets for different analysis strategies.

| Preset | Description |
|--------|-------------|
| `default` | Default settings (same as top-level) |
| `conservative` | Lower surge threshold (30%) |
| `aggressive` | Higher surge threshold (100%) |
| `sp500` | Settings optimized for S&P 500 stocks |
| `sec_filers` | Settings optimized for SEC filing companies |

**Example:**
```yaml
analysis:
  # ... other settings ...
  presets:
    aggressive:
      years_back: 3
      min_surge_percent: 100.0
```

### Stock Selection Settings

The `stock_selection` section controls which stocks are analyzed.

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `source` | string | `"manual"` | Source of stock tickers (manual, sp500, nasdaq100, dow_jones, custom, market_cap, sector) |
| `max_stocks` | integer | `0` | Maximum number of stocks to analyze (0 for no limit) |
| `use_cache` | boolean | `true` | Whether to use cached stock lists |
| `custom_file_path` | string | `""` | Path to custom stock list file (for source: "custom") |
| `min_market_cap` | integer | `1000000000` | Minimum market cap in USD (for source: "market_cap") |
| `max_market_cap` | integer or null | `null` | Maximum market cap in USD (for source: "market_cap") |
| `sector` | string | `""` | Sector name (for source: "sector") |

**Example:**
```yaml
stock_selection:
  source: "sp500"
  max_stocks: 100
  use_cache: true
```

### Output Settings

The `output` section controls how results are saved.

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `results_dir` | string | `"results/surge_analyzer"` | Output directory for results |
| `format` | string | `"csv"` | Output format (console, csv) |
| `csv_path` | string | `"surge_analysis.csv"` | CSV file path (relative to results_dir) |

**Example:**
```yaml
output:
  results_dir: "results/surge_analyzer"
  format: "csv"
  csv_path: "surge_analysis_2023.csv"
```

### Dashboard Settings

The `dashboard` section controls the behavior of the interactive dashboard.

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `port` | integer | `8050` | Port to run the dashboard on |
| `debug` | boolean | `false` | Whether to run in debug mode |
| `theme` | string | `"light"` | Theme (light, dark) |

**Example:**
```yaml
dashboard:
  port: 8080
  debug: true
  theme: "dark"
```

## Configuration Overrides

The configuration file provides the base settings, but you can override these settings in several ways.

### Command-Line Arguments

You can override configuration settings using command-line arguments when running the pipeline:

```bash
python bin/surge_analyzer/run_surge_analyzer.py --years 3 --min-surge 75.0 --tickers "AAPL,MSFT,GOOGL"
```

The following command-line arguments are supported:

| Argument | Description |
|----------|-------------|
| `--config`, `-c` | Path to configuration file |
| `--preset`, `-pr` | Configuration preset to use |
| `--tickers`, `-t` | Comma-separated list of stock tickers to analyze |
| `--years`, `-y` | Number of years to look back |
| `--min-surge`, `-s` | Minimum percentage surge to identify |
| `--output`, `-o` | Output CSV file path |
| `--results`, `-r` | Path to results file for dashboard |
| `--port`, `-p` | Port to run the dashboard on |
| `--debug`, `-d` | Run dashboard in debug mode |
| `--disable-file-logging` | Disable logging to a file (used by shell scripts) |

### Presets

You can use predefined presets to quickly switch between different configurations:

```bash
python bin/surge_analyzer/run_surge_analyzer.py --preset aggressive
```

Or when using the shell script:

```bash
# On Linux/Mac:
./scripts/surge_analyzer/run_surge_analyzer.sh --preset aggressive

# On Windows:
scripts\surge_analyzer\run_surge_analyzer.bat --preset aggressive
```

## Example Configurations

### Basic Configuration

A simple configuration for analyzing S&P 500 stocks:

```yaml
data:
  data_dir: "data/surge_analyzer"
  interval: "1d"

analysis:
  years_back: 5
  min_surge_percent: 50.0

stock_selection:
  source: "sp500"
  max_stocks: 500

output:
  format: "csv"
```

### Configuration for High-Growth Tech Stocks

A configuration focused on identifying high-growth technology stocks:

```yaml
data:
  data_dir: "data/surge_analyzer"
  interval: "1d"

analysis:
  years_back: 3
  min_surge_percent: 100.0

stock_selection:
  source: "sector"
  sector: "Technology"
  max_stocks: 200

output:
  format: "csv"
  csv_path: "tech_surge_stocks.csv"
```

### Configuration for Conservative Analysis

A configuration with a lower surge threshold for more conservative analysis:

```yaml
data:
  data_dir: "data/surge_analyzer"
  interval: "1d"

analysis:
  years_back: 5
  min_surge_percent: 30.0

stock_selection:
  source: "market_cap"
  min_market_cap: 10000000000  # $10 billion
  max_stocks: 100

output:
  format: "csv"
  csv_path: "conservative_surge_stocks.csv"
```

### Configuration for Custom Stock List

A configuration for analyzing a custom list of stocks:

```yaml
data:
  data_dir: "data/surge_analyzer"
  interval: "1d"

analysis:
  years_back: 5
  min_surge_percent: 50.0

stock_selection:
  source: "custom"
  custom_file_path: "data/my_watchlist.csv"

output:
  format: "csv"
  csv_path: "watchlist_surge_stocks.csv"
```

## Best Practices

### General Guidelines

1. **Use Descriptive Paths**: Use clear, descriptive paths for data and results directories
2. **Limit Batch Size**: Keep batch sizes reasonable (50-100) to avoid memory issues
3. **Use Caching**: Enable caching to improve performance for repeated runs
4. **Start Conservative**: Begin with conservative settings and adjust as needed

### Performance Optimization

1. **Adjust Batch Size**: Increase or decrease batch size based on available memory
2. **Limit Stock Universe**: Use `max_stocks` to limit the number of stocks analyzed
3. **Use Shorter Timeframes**: Reduce `years_back` for faster analysis
4. **Enable Caching**: Always enable caching for stock lists and data

### Configuration Management

1. **Version Control**: Keep configuration files in version control
2. **Document Changes**: Document changes to configuration in commit messages
3. **Use Presets**: Create presets for commonly used configurations
4. **Validate Configuration**: Validate configuration before running the pipeline

## Troubleshooting

### Common Issues

1. **Missing Data Directory**: Ensure that the data directory exists and is writable
2. **Invalid Stock Source**: Verify that the stock selection source is valid
3. **API Rate Limiting**: If you encounter rate limiting, reduce batch size and increase delays
4. **Memory Issues**: If you encounter memory issues, reduce batch size or max_stocks

### Configuration Validation

The Surge Analyzer validates the configuration before running the pipeline. If there are issues with the configuration, you will see error messages indicating the problem.

Common validation errors:

- **Invalid data interval**: The interval must be one of: 1d, 1wk, 1mo
- **Invalid stock source**: The source must be one of: manual, sp500, nasdaq100, dow_jones, custom, market_cap, sector
- **Missing custom file path**: When using a custom stock source, you must provide a custom_file_path
- **Invalid sector name**: When using a sector stock source, you must provide a valid sector name
