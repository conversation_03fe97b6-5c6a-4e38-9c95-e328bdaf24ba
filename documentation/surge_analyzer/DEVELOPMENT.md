# Surge Analyzer Development Guide

This document provides technical information for developers who want to understand, modify, or contribute to the Surge Analyzer pipeline.

## Code Organization

The Surge Analyzer codebase follows a modular structure with clear separation of concerns:

```
surge-analyzer/
├── bin/                    # Executable Python scripts
│   └── surge_analyzer/     # Surge analyzer executables
│       ├── run_surge_analyzer.py
│       └── run_surge_analyzer_tests.py
├── config/                 # Configuration files
│   └── surge_analyzer/     # Surge analyzer configurations
│       └── surge_analyzer_config.yaml
├── data/                   # Data storage
│   └── surge_analyzer/     # Data for surge analyzer
│       └── stock_lists/    # Cached stock lists
├── documentation/          # Documentation files
│   └── surge_analyzer/     # Surge analyzer documentation
│       ├── AUGMENT_SUMMARY.md
│       ├── README.md
│       ├── API_REFERENCE.md
│       ├── CONFIGURATION.md
│       └── DEVELOPMENT.md
├── logs/                   # Log files
│   └── surge_analyzer/     # Surge analyzer logs
├── requirements/           # Dependency specifications
│   └── surge_analyzer/     # Surge analyzer requirements
│       ├── surge_analyzer_requirements.txt
│       └── surge_analyzer_test_requirements.txt
├── results/                # Analysis results
│   └── surge_analyzer/     # Results from surge analyzer
├── scripts/                # Shell scripts
│   └── surge_analyzer/     # Surge analyzer scripts
│       ├── run_surge_analyzer.sh
│       ├── run_surge_analyzer.bat
│       ├── run_surge_analyzer_tests.sh
│       └── run_surge_analyzer_tests.bat
├── src/                    # Source code
│   └── surge_analyzer/     # Surge analyzer code
│       ├── analyzer.py     # Core surge analyzer
│       ├── data_fetcher.py # Data fetching functionality
│       ├── stock_list_fetcher.py # Stock list acquisition
│       ├── technical_indicators.py # Technical analysis
│       ├── dashboard.py    # Dashboard implementation
│       ├── utils/          # Utility functions
│       │   ├── config_loader.py
│       │   ├── data_utils.py
│       │   └── file_utils.py
│       └── __init__.py
├── tests/                  # Test suite
│   └── surge_analyzer/     # Tests for surge analyzer
│       ├── functional/     # Functional tests
│       │   ├── test_dashboard.py
│       │   └── test_surge_pipeline.py
│       ├── unit/           # Unit tests
│       │   ├── test_analyzer.py
│       │   ├── test_data_fetcher.py
│       │   ├── test_stock_list_fetcher.py
│       │   ├── test_technical_indicators.py
│       │   └── test_utils.py
│       └── fixtures/       # Test fixtures
│           └── sample_data.py
└── venvs/                  # Virtual environments
    └── surge_analyzer/     # Surge analyzer virtual environment
```

### Key Modules

1. **src/surge_analyzer/**
   - `analyzer.py`: Core analysis logic for identifying and analyzing surges
   - `data_fetcher.py`: Retrieves historical stock data
   - `stock_list_fetcher.py`: Fetches stock lists from various sources
   - `technical_indicators.py`: Implementation of technical analysis indicators
   - `dashboard.py`: Interactive dashboard implementation

2. **src/surge_analyzer/utils/**
   - `config_loader.py`: Configuration loading and management
   - `data_utils.py`: Utilities for data manipulation
   - `file_utils.py`: Utilities for file operations

3. **bin/surge_analyzer/**
   - `run_surge_analyzer.py`: Main entry point for the pipeline
   - `run_surge_analyzer_tests.py`: Entry point for running tests

4. **scripts/surge_analyzer/**
   - `run_surge_analyzer.sh`: Shell script for running the pipeline (Linux/Mac)
   - `run_surge_analyzer.bat`: Batch script for running the pipeline (Windows)
   - `run_surge_analyzer_tests.sh`: Shell script for running tests (Linux/Mac)
   - `run_surge_analyzer_tests.bat`: Batch script for running tests (Windows)

## Development Environment Setup

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Git (for version control)
- A code editor (VS Code, PyCharm, etc.)

### Setting Up the Development Environment

1. **Clone the Repository**

```bash
git clone https://github.com/yourusername/surge-analyzer.git
cd surge-analyzer
```

2. **Create a Virtual Environment**

```bash
# Create a virtual environment
python -m venv venvs/surge_analyzer

# Activate the virtual environment
# On Windows:
venvs\surge_analyzer\Scripts\activate
# On macOS/Linux:
source venvs/surge_analyzer/bin/activate
```

3. **Install Development Dependencies**

```bash
# Install runtime dependencies
pip install -r requirements/surge_analyzer_requirements.txt

# Install test dependencies
pip install -r requirements/surge_analyzer_test_requirements.txt

# Install development tools (optional)
pip install black flake8 mypy
```

4. **Create Required Directories**

```bash
mkdir -p data/surge_analyzer/stock_lists
mkdir -p results/surge_analyzer
mkdir -p logs/surge_analyzer
```

## Development Workflow

### Running the Pipeline

During development, you can run the pipeline directly using Python:

```bash
python bin/surge_analyzer/run_surge_analyzer.py
```

Or using the shell script:

```bash
# On Linux/Mac:
./scripts/surge_analyzer/run_surge_analyzer.sh

# On Windows:
scripts\surge_analyzer\run_surge_analyzer.bat
```

### Running Tests

To run the test suite:

```bash
# Run all tests
python bin/surge_analyzer/run_surge_analyzer_tests.py

# Run only unit tests
python bin/surge_analyzer/run_surge_analyzer_tests.py --unit

# Run only functional tests
python bin/surge_analyzer/run_surge_analyzer_tests.py --functional

# Run tests for a specific module
python bin/surge_analyzer/run_surge_analyzer_tests.py --module data_fetcher

# Run tests with coverage
python bin/surge_analyzer/run_surge_analyzer_tests.py --coverage
```

Or using the shell script:

```bash
# On Linux/Mac:
./scripts/surge_analyzer/run_surge_analyzer_tests.sh

# On Windows:
scripts\surge_analyzer\run_surge_analyzer_tests.bat
```

### Code Style

The codebase follows PEP 8 style guidelines with the following modifications:

- Maximum line length: 100 characters
- Use of type hints for function parameters and return values
- Docstrings in Google style format

You can use Black to automatically format your code:

```bash
black src/ tests/ bin/
```

And Flake8 to check for style issues:

```bash
flake8 src/ tests/ bin/
```

## Testing

The Surge Analyzer uses pytest for testing. The test suite is organized into:

- **Unit Tests**: Test individual components in isolation
- **Functional Tests**: Test the integration of multiple components

### Test Structure

```
tests/surge_analyzer/
├── conftest.py             # Shared test fixtures
├── functional/             # Functional tests
│   ├── test_dashboard.py   # Dashboard tests
│   └── test_surge_pipeline.py  # End-to-end pipeline tests
└── unit/                   # Unit tests
    ├── analysis/           # Tests for analysis components
    │   └── test_surge_analyzer.py
    ├── data/               # Tests for data components
    │   ├── test_data_fetcher.py
    │   └── test_stock_list_fetcher.py
    ├── technical_analysis/ # Tests for technical analysis
    │   └── test_technical_indicators.py
    └── utils/              # Tests for utilities
        └── test_config_loader.py
```

### Writing Tests

When writing tests, follow these guidelines:

1. **Use Fixtures**: Utilize pytest fixtures for setup and teardown
2. **Mock External Dependencies**: Use mocks for external APIs and services
3. **Test Edge Cases**: Include tests for error conditions and edge cases
4. **Maintain Independence**: Tests should not depend on each other
5. **Use Descriptive Names**: Test names should describe what they're testing

Example of a unit test:

```python
def test_find_surge_stocks(mock_stock_data):
    # Arrange
    analyzer = SurgeAnalyzer()
    min_surge_percent = 50.0

    # Act
    surge_stocks = analyzer.find_surge_stocks(mock_stock_data, min_surge_percent)

    # Assert
    assert len(surge_stocks) == 2
    assert "AAPL" in surge_stocks
    assert surge_stocks["AAPL"] > min_surge_percent
```

## Debugging

### Logging

The Surge Analyzer uses a comprehensive logging system that combines Python's built-in logging module with shell script output capture:

1. **Shell Script Logging**:
   - The shell scripts (`run_surge_analyzer.sh` and `run_surge_analyzer.bat`) create log files in the `logs/surge_analyzer/` directory
   - They capture all terminal output (stdout and stderr) using the `tee` command
   - Log files are named with timestamps: `pipeline_run_YYYYMMDD_HHMMSS.log`

2. **Python Logging**:
   - The Python script uses the built-in logging module
   - When run directly, it creates its own log file in the `logs/surge_analyzer/` directory
   - When run through the shell scripts, it disables file logging to avoid duplicate log files (using the `--disable-file-logging` flag)

3. **Log Content**:
   - Pipeline initialization and configuration
   - Stock list acquisition
   - Data fetching progress
   - Analysis results
   - Dashboard operation
   - Error messages and warnings

To enable debug logging when running the Python script directly:

```bash
python bin/surge_analyzer/run_surge_analyzer.py --log-level DEBUG
```

When using the shell scripts, the log file path is displayed at the end of execution:

```
Log file is available at: logs/surge_analyzer/pipeline_run_YYYYMMDD_HHMMSS.log
```

### Debugging the Dashboard

To run the dashboard in debug mode:

```bash
python bin/surge_analyzer/run_surge_analyzer.py --mode dashboard --debug
```

Or using the shell script:

```bash
# On Linux/Mac:
./scripts/surge_analyzer/run_surge_analyzer.sh --dashboard --debug

# On Windows:
scripts\surge_analyzer\run_surge_analyzer.bat --dashboard --debug
```

This enables:
- Hot reloading of code changes
- More detailed error messages
- Debug toolbar in the browser

## Contribution Guidelines

### Pull Request Process

1. **Fork the Repository**: Create your own fork of the repository
2. **Create a Branch**: Create a feature branch for your changes
3. **Make Changes**: Implement your changes following the code style guidelines
4. **Write Tests**: Add tests for your changes
5. **Run Tests**: Ensure all tests pass
6. **Update Documentation**: Update relevant documentation
7. **Submit Pull Request**: Create a pull request with a clear description of your changes

### Commit Message Guidelines

Follow the conventional commits format:

```
<type>(<scope>): <subject>

<body>

<footer>
```

Where `<type>` is one of:
- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code changes that neither fix bugs nor add features
- **test**: Adding or modifying tests
- **chore**: Changes to the build process or auxiliary tools

### Code Review Process

All pull requests will be reviewed by at least one maintainer. The review process includes:

1. **Functionality**: Does the change work as expected?
2. **Code Quality**: Does the code follow the style guidelines?
3. **Tests**: Are there appropriate tests for the changes?
4. **Documentation**: Is the documentation updated?

## Release Process

1. **Version Bump**: Update version numbers in relevant files
2. **Changelog**: Update the changelog with the changes in the new version
3. **Tag Release**: Create a git tag for the new version
4. **Build**: Build the distribution packages
5. **Publish**: Publish the packages to the appropriate channels

## Troubleshooting

### Common Issues

1. **API Rate Limiting**: Yahoo Finance may rate-limit requests. Use caching and batch processing to minimize requests.
2. **Memory Usage**: Processing large datasets can consume significant memory. Use batch processing and monitor memory usage.
3. **Dashboard Performance**: The dashboard may become slow with large datasets. Consider filtering or aggregating data.

### Getting Help

If you encounter issues:

1. Check the logs for error messages
2. Review the documentation
3. Search for similar issues in the issue tracker
4. Create a new issue with detailed information about the problem
