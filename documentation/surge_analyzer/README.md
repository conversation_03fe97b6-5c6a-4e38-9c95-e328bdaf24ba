# Surge Analyzer

A powerful tool for identifying and analyzing stocks with significant price surges.

## Overview

The Surge Analyzer is a comprehensive pipeline designed to help investors and traders identify stocks that have experienced substantial price increases over a specified time period. By analyzing historical price data, the tool identifies stocks with price surges exceeding a configurable threshold and provides detailed metrics to help evaluate these opportunities.

## Features

- **Automated Stock List Acquisition**: Fetch stock lists from various sources (S&P 500, NASDAQ 100, NYSE, etc.)
- **Historical Data Analysis**: Analyze years of price data to identify significant surge patterns
- **Technical Analysis**: Calculate key technical indicators (moving averages, RSI, MACD, etc.)
- **Detailed Metrics**: Get comprehensive statistics including annualized returns, volatility, and drawdowns
- **Interactive Dashboard**: Explore results through an intuitive web interface with interactive charts
- **Flexible Configuration**: Customize analysis parameters through configuration files or command-line options

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Git (optional, for cloning the repository)

### Step 1: Clone the Repository (or download the source code)

```bash
git clone https://github.com/yourusername/surge-analyzer.git
cd surge-analyzer
```

### Step 2: Set Up a Virtual Environment (Recommended)

```bash
# Create a virtual environment
python -m venv venvs/surge_analyzer

# Activate the virtual environment
# On Windows:
venvs\surge_analyzer\Scripts\activate
# On macOS/Linux:
source venvs/surge_analyzer/bin/activate
```

### Step 3: Install Dependencies

```bash
pip install -r requirements/surge_analyzer_requirements.txt
```

### Step 4: Install Test Dependencies (Optional)

```bash
pip install -r requirements/surge_analyzer_test_requirements.txt
```

## Basic Usage

### Using the Command-Line Interface

The simplest way to run the Surge Analyzer is through the provided shell script:

```bash
# On Linux/Mac:
./scripts/surge_analyzer/run_surge_analyzer.sh

# On Windows:
scripts\surge_analyzer\run_surge_analyzer.bat
```

This will:
1. Create a virtual environment in `venvs/surge_analyzer` if it doesn't exist
2. Install all required dependencies from `requirements/surge_analyzer/surge_analyzer_requirements.txt`
3. Run the surge analyzer pipeline with default settings (S&P 500 mode)
4. Launch the interactive dashboard

### Command-Line Options

You can customize the analysis by passing options to the script:

```bash
# Run only the pipeline (no dashboard)
./scripts/surge_analyzer/run_surge_analyzer.sh --pipeline

# Run only the dashboard
./scripts/surge_analyzer/run_surge_analyzer.sh --dashboard

# Specify a custom configuration file
./scripts/surge_analyzer/run_surge_analyzer.sh --config path/to/custom_config.yaml

# Use a specific preset
./scripts/surge_analyzer/run_surge_analyzer.sh --preset aggressive

# Analyze specific tickers
./scripts/surge_analyzer/run_surge_analyzer.sh --tickers "AAPL,MSFT,GOOGL"

# Specify the number of years to look back
./scripts/surge_analyzer/run_surge_analyzer.sh --years 3

# Set the minimum surge percentage
./scripts/surge_analyzer/run_surge_analyzer.sh --min-surge 50
```

### Using the Python API

You can also use the Surge Analyzer programmatically in your Python code:

```python
from src.surge_analyzer.analyzer import SurgeAnalyzer
from src.surge_analyzer.data_fetcher import StockDataFetcher
from src.surge_analyzer.stock_list_fetcher import StockListFetcher

# Initialize components
data_fetcher = StockDataFetcher(data_dir="data/surge_analyzer")
list_fetcher = StockListFetcher(cache_dir="data/surge_analyzer/stock_lists")
analyzer = SurgeAnalyzer()

# Get stock list (e.g., S&P 500)
tickers = list_fetcher.fetch_sp500_tickers()

# Fetch historical data
stock_data = data_fetcher.fetch_multiple_stocks(tickers, years_back=5)

# Analyze for surges
min_surge_percent = 100.0
surge_stocks = analyzer.find_surge_stocks(stock_data, min_surge_percent)

# Print results
for ticker, percent_change in surge_stocks.items():
    print(f"{ticker}: {percent_change:.2f}%")

# Save results
analyzer.save_results(surge_stocks, "results/surge_analyzer/surge_analysis.csv")
```

## Configuration

The Surge Analyzer can be configured through a YAML configuration file. The default configuration is located at `config/surge_analyzer/surge_analyzer_config.yaml`.

Here's an example of a basic configuration:

```yaml
# Data settings
data:
  data_dir: "data/surge_analyzer"
  stock_lists_cache_dir: "data/surge_analyzer/stock_lists"
  interval: "1d"

# Analysis settings
analysis:
  years_back: 5
  min_surge_percent: 100.0  # Default threshold for surge detection
  price_col: "Close"
  batch_size: 50

# Stock selection settings
stock_selection:
  source: "sp500"  # Default source is S&P 500
  max_stocks: 500
  use_cache: true

# Output settings
output:
  results_dir: "results/surge_analyzer"
  format: "csv"
```

You can override these settings using command-line arguments when running the pipeline. For more detailed configuration options, see the [Configuration Guide](CONFIGURATION.md).

## Dashboard

The Surge Analyzer includes an interactive dashboard built with Dash and Plotly. To launch the dashboard:

```bash
./scripts/surge_analyzer/run_surge_analyzer.sh --dashboard
```

The dashboard provides:

- A table of surge stocks with sortable metrics
- Interactive price charts with technical indicators
- Detailed analysis of surge patterns
- Comparison tools for multiple stocks

The dashboard is organized into multiple tabs:
- **Overview**: Summary of all surge stocks with key metrics
- **Stock Details**: Detailed analysis of a selected stock
- **Technical Analysis**: Technical indicators and chart patterns
- **Comparison**: Side-by-side comparison of multiple stocks

## Running Tests

The Surge Analyzer includes a comprehensive test suite. To run the tests:

```bash
# On Linux/Mac:
./scripts/surge_analyzer/run_surge_analyzer_tests.sh

# On Windows:
scripts\surge_analyzer\run_surge_analyzer_tests.bat
```

This will:
1. Create a test virtual environment if it doesn't exist
2. Install all required dependencies, including test dependencies
3. Run the test suite
4. Generate test reports

## Documentation

For more detailed information, refer to the following documentation:

- [Architecture Overview](AUGMENT_SUMMARY.md): Detailed description of the system architecture
- [Development Guide](DEVELOPMENT.md): Information for developers
- [API Reference](API_REFERENCE.md): Detailed documentation of classes and methods
- [Configuration Guide](CONFIGURATION.md): Comprehensive guide to configuration options

## Project Structure

The project follows a well-organized directory structure:

```
surge-analyzer/
├── bin/                    # Executable Python scripts
│   └── surge_analyzer/     # Surge analyzer executables
├── config/                 # Configuration files
│   └── surge_analyzer/     # Surge analyzer configurations
├── data/                   # Data storage
│   └── surge_analyzer/     # Data for surge analyzer
├── documentation/          # Documentation files
│   └── surge_analyzer/     # Surge analyzer documentation
├── logs/                   # Log files
│   └── surge_analyzer/     # Surge analyzer logs
├── requirements/           # Dependency specifications
│   └── surge_analyzer/     # Surge analyzer requirements
├── results/                # Analysis results
│   └── surge_analyzer/     # Results from surge analyzer
├── scripts/                # Shell scripts
│   └── surge_analyzer/     # Surge analyzer scripts
├── src/                    # Source code
│   └── surge_analyzer/     # Surge analyzer code
├── tests/                  # Test suite
│   └── surge_analyzer/     # Tests for surge analyzer
└── venvs/                  # Virtual environments
    └── surge_analyzer/     # Surge analyzer virtual environment
```

This structure ensures that all components related to the surge analyzer are properly organized and isolated, making the codebase easy to navigate and maintain.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Yahoo Finance for providing the historical stock data
- Dash and Plotly for the interactive visualization components
- The open-source community for the various libraries used in this project
