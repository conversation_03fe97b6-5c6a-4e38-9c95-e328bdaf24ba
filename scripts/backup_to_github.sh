#!/bin/bash
# ============================================================================
# GitHub Secure Backup Script
#
# This script creates a secure backup of the entire project and pushes it to
# the main branch on GitHub. It includes a timestamp in the commit message
# for easy reference.
#
# Usage:
#   ./scripts/backup_to_github.sh                  # Standard backup
#   ./scripts/backup_to_github.sh "Custom message" # Backup with custom message
# ============================================================================

# Exit immediately if a command exits with a non-zero status
set -e

# Get the directory of this script and the project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Change to the project root directory
cd "$PROJECT_ROOT"

# Print header function
print_header() {
    echo ""
    echo "=== $1 ==="
    echo ""
}

# Print success message
print_success() {
    echo "✓ $1"
}

# Print error message
print_error() {
    echo "✗ $1"
}

# Check if git is installed
if ! command -v git &> /dev/null; then
    print_error "Git is not installed. Please install git and try again."
    exit 1
fi

# Check if the directory is a git repository
if [ ! -d ".git" ]; then
    print_error "This directory is not a git repository. Please initialize git first."
    exit 1
fi

# Get the current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Create timestamp for the backup
TIMESTAMP=$(date +"%Y-%m-%d_%H-%M-%S")

# Set the commit message
if [ -z "$1" ]; then
    COMMIT_MESSAGE="SECURE BACKUP: Automated backup created on $TIMESTAMP"
else
    COMMIT_MESSAGE="SECURE BACKUP: $1 (Backup created on $TIMESTAMP)"
fi

print_header "Starting GitHub Backup Process"

# Check for uncommitted changes
if git diff-index --quiet HEAD --; then
    echo "No changes detected in the working directory."
else
    echo "Changes detected in the working directory."
fi

# Add all files to git
print_header "Adding Files to Git"
git add -A
print_success "Added all files to git staging area"

# Create a backup commit
print_header "Creating Backup Commit"
git commit -m "$COMMIT_MESSAGE"
print_success "Created backup commit with message: $COMMIT_MESSAGE"

# Get the remote repository URL
REMOTE_URL=$(git remote get-url origin 2>/dev/null || echo "")

if [ -z "$REMOTE_URL" ]; then
    print_error "No remote repository found. Please set up a remote repository first."
    print_error "You can add a remote with: git remote add origin <repository-url>"
    exit 1
fi

# Push to GitHub
print_header "Pushing to GitHub"
echo "Pushing to remote repository: $REMOTE_URL"
echo "Branch: main"

# Check if we're on the main branch
if [ "$CURRENT_BRANCH" != "main" ]; then
    echo "Currently on branch: $CURRENT_BRANCH"
    echo "Switching to main branch..."
    
    # Check if main branch exists locally
    if git show-ref --verify --quiet refs/heads/main; then
        git checkout main
    else
        # Check if main branch exists on remote
        if git ls-remote --exit-code --heads origin main; then
            git checkout -b main origin/main
        else
            # Create main branch if it doesn't exist
            git checkout -b main
        fi
    fi
    
    # Cherry-pick the backup commit from the original branch
    BACKUP_COMMIT=$(git rev-parse HEAD@{1})
    git cherry-pick $BACKUP_COMMIT
fi

# Push to GitHub
git push origin main

print_header "Backup Complete"
print_success "Successfully pushed backup to GitHub"
echo "Commit: $COMMIT_MESSAGE"
echo "Branch: main"
echo "Remote: $REMOTE_URL"

# Return to the original branch if needed
if [ "$CURRENT_BRANCH" != "main" ] && [ "$CURRENT_BRANCH" != "HEAD" ]; then
    echo "Returning to original branch: $CURRENT_BRANCH"
    git checkout $CURRENT_BRANCH
fi

print_success "Backup process completed successfully"
