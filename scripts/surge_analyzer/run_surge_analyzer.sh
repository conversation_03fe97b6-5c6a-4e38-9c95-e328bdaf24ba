#!/bin/bash
# ============================================================================
# Surge Analyzer Runner Script
#
# This script:
# 1. Creates a virtual environment for the surge analyzer
# 2. Installs all required dependencies
# 3. Runs the surge analyzer pipeline or dashboard
# 4. Deactivates the virtual environment
#
# Usage:
#   ./run_surge_analyzer.sh                  # Run both pipeline and dashboard
#   ./run_surge_analyzer.sh --dashboard      # Run only the dashboard
#   ./run_surge_analyzer.sh --pipeline       # Run only the pipeline
# ============================================================================

# Set up error handling
set -e  # Exit immediately if a command exits with a non-zero status
trap 'echo "Error occurred at line $LINENO. Command: $BASH_COMMAND"' ERR

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
VENVS_DIR="$PROJECT_ROOT/venvs"
SURGE_VENV_DIR="$VENVS_DIR/surge_analyzer"
REQUIREMENTS_FILE="$PROJECT_ROOT/requirements/surge_analyzer/surge_analyzer_requirements.txt"
SURGE_SCRIPT="$PROJECT_ROOT/bin/surge_analyzer/run_surge_analyzer.py"

# Function to print section headers
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning messages
print_warning() {
    echo -e "${YELLOW}! $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if Python 3 is installed
print_header "Checking Python Installation"
if command -v python3 &>/dev/null; then
    PYTHON_VERSION=$(python3 --version)
    print_success "Python is installed: $PYTHON_VERSION"
else
    print_error "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check if pip is installed
if python3 -m pip --version &>/dev/null; then
    PIP_VERSION=$(python3 -m pip --version)
    print_success "Pip is installed: $PIP_VERSION"
else
    print_error "Pip is not installed. Please install pip and try again."
    exit 1
fi

# Check if venv module is available
if python3 -c "import venv" &>/dev/null; then
    print_success "Python venv module is available"
else
    print_error "Python venv module is not available. Please install it and try again."
    exit 1
fi

# Create venvs directory if it doesn't exist
print_header "Setting Up Virtual Environment"
if [ ! -d "$VENVS_DIR" ]; then
    echo "Creating venvs directory..."
    mkdir -p "$VENVS_DIR"
    print_success "Created venvs directory at $VENVS_DIR"
else
    print_warning "Venvs directory already exists at $VENVS_DIR"
fi

# Check if surge virtual environment already exists
if [ -d "$SURGE_VENV_DIR" ]; then
    print_warning "Surge virtual environment already exists at $SURGE_VENV_DIR"
    read -p "Do you want to recreate it? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Removing existing surge virtual environment..."
        rm -rf "$SURGE_VENV_DIR"
        print_success "Removed existing surge virtual environment"
    else
        print_warning "Using existing surge virtual environment"
    fi
fi

# Create surge virtual environment if it doesn't exist
if [ ! -d "$SURGE_VENV_DIR" ]; then
    echo "Creating surge virtual environment..."
    python3 -m venv "$SURGE_VENV_DIR"
    print_success "Created surge virtual environment at $SURGE_VENV_DIR"
fi

# Activate the virtual environment
echo "Activating surge virtual environment..."
source "$SURGE_VENV_DIR/bin/activate"
print_success "Activated surge virtual environment"

# Upgrade pip
print_header "Installing Dependencies"
echo "Upgrading pip..."
pip install --upgrade pip
print_success "Upgraded pip"

# Install requirements
echo "Installing requirements from $REQUIREMENTS_FILE..."
if [ -f "$REQUIREMENTS_FILE" ]; then
    pip install -r "$REQUIREMENTS_FILE"
    print_success "Installed all requirements"
else
    print_error "Requirements file not found at $REQUIREMENTS_FILE"
    deactivate
    exit 1
fi

# Create necessary directories
print_header "Creating Necessary Directories"
mkdir -p "$PROJECT_ROOT/data/surge_analyzer/stock_lists"
mkdir -p "$PROJECT_ROOT/results/surge_analyzer"
print_success "Created data and results directories"

# Parse command line arguments
RUN_MODE="both"
EXTRA_ARGS=()

# Process command line arguments
for arg in "$@"; do
    case $arg in
        --dashboard)
            RUN_MODE="dashboard"
            shift
            ;;
        --pipeline)
            RUN_MODE="pipeline"
            shift
            ;;
        *)
            EXTRA_ARGS+=("$arg")
            ;;
    esac
done

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_ROOT/logs/surge_analyzer"

# Create a timestamp for the log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$PROJECT_ROOT/logs/surge_analyzer/pipeline_run_${TIMESTAMP}.log"

# Run the surge analyzer
print_header "Running Surge Analyzer"
if [ -f "$SURGE_SCRIPT" ]; then
    if [ "$RUN_MODE" = "dashboard" ]; then
        echo "Running surge analyzer dashboard only..."
        echo "Running surge analyzer dashboard only..." >> "$LOG_FILE"
        # Run the script and capture output to both terminal and log file
        python "$SURGE_SCRIPT" --disable-file-logging --mode dashboard "${EXTRA_ARGS[@]}" 2>&1 | tee -a "$LOG_FILE"
    elif [ "$RUN_MODE" = "pipeline" ]; then
        echo "Running surge analyzer pipeline only..."
        echo "Running surge analyzer pipeline only..." >> "$LOG_FILE"
        # Run the script and capture output to both terminal and log file
        python "$SURGE_SCRIPT" --disable-file-logging --mode pipeline "${EXTRA_ARGS[@]}" 2>&1 | tee -a "$LOG_FILE"
    else
        echo "Running surge analyzer pipeline and dashboard..."
        echo "Running surge analyzer pipeline and dashboard..." >> "$LOG_FILE"
        # Run the script and capture output to both terminal and log file
        python "$SURGE_SCRIPT" --disable-file-logging --mode both "${EXTRA_ARGS[@]}" 2>&1 | tee -a "$LOG_FILE"
    fi

    # When using a pipe, the exit code is from the last command in the pipe (tee)
    # Use PIPESTATUS to get the exit code of the Python script
    SURGE_EXIT_CODE=${PIPESTATUS[0]}

    if [ $SURGE_EXIT_CODE -eq 0 ]; then
        print_success "Surge analyzer completed successfully"
        echo "Surge analyzer completed successfully" >> "$LOG_FILE"
    else
        print_error "Surge analyzer failed with exit code $SURGE_EXIT_CODE"
        echo "Surge analyzer failed with exit code $SURGE_EXIT_CODE" >> "$LOG_FILE"
    fi
else
    print_error "Surge analyzer script not found at $SURGE_SCRIPT"
    echo "Surge analyzer script not found at $SURGE_SCRIPT" >> "$LOG_FILE"
    SURGE_EXIT_CODE=1
fi

# Deactivate the virtual environment
print_header "Cleaning Up"
echo "Deactivating surge virtual environment..."
deactivate
print_success "Deactivated surge virtual environment"

# Final message
if [ $SURGE_EXIT_CODE -eq 0 ]; then
    print_header "Surge Analyzer Completed Successfully"
    echo "Results are available in the results/surge_analyzer directory"
    echo "Log file is available at: $LOG_FILE"
else
    print_header "Surge Analyzer Failed"
    echo "Please check the error messages above for more information"
    echo "Log file is available at: $LOG_FILE"
fi

exit $SURGE_EXIT_CODE
