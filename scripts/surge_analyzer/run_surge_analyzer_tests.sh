#!/bin/bash
# Run tests for the Surge Analyzer

# Define paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
VENVS_DIR="$PROJECT_ROOT/venvs"
SURGE_VENV_DIR="$VENVS_DIR/surge_analyzer"

# Set up virtual environment if it doesn't exist
if [ ! -d "$SURGE_VENV_DIR" ]; then
    echo "Creating virtual environment..."
    mkdir -p "$VENVS_DIR"
    python3 -m venv "$SURGE_VENV_DIR"
fi

# Activate virtual environment
source "$SURGE_VENV_DIR/bin/activate"

# Install project and test dependencies
echo "Installing project dependencies..."
pip install -r "$PROJECT_ROOT/requirements/surge_analyzer/surge_analyzer_requirements.txt"

echo "Installing test dependencies..."
pip install -r "$PROJECT_ROOT/requirements/surge_analyzer/surge_analyzer_test_requirements.txt"

# Run the tests
python "$PROJECT_ROOT/bin/surge_analyzer/run_surge_analyzer_tests.py" "$@"

# Deactivate virtual environment
deactivate
