@echo off
setlocal enabledelayedexpansion
:: ============================================================================
:: Surge Analyzer Runner Script for Windows
::
:: This script:
:: 1. Creates a virtual environment for the surge analyzer
:: 2. Installs all required dependencies
:: 3. Runs the surge analyzer pipeline or dashboard
:: 4. Deactivates the virtual environment
::
:: Usage:
::   run_surge_analyzer.bat                  # Run both pipeline and dashboard
::   run_surge_analyzer.bat --dashboard      # Run only the dashboard
::   run_surge_analyzer.bat --pipeline       # Run only the pipeline
:: ============================================================================

echo === Surge Analyzer Runner Script for Windows ===

:: Define paths
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%..\..\"
set "VENVS_DIR=%PROJECT_ROOT%venvs"
set "SURGE_VENV_DIR=%VENVS_DIR%\surge_analyzer"
set "REQUIREMENTS_FILE=%PROJECT_ROOT%requirements\surge_analyzer\surge_analyzer_requirements.txt"
set "SURGE_SCRIPT=%PROJECT_ROOT%bin\surge_analyzer\run_surge_analyzer.py"

:: Check if Python is installed
echo.
echo === Checking Python Installation ===
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python is not installed or not in PATH. Please install Python and try again.
    exit /b 1
)
echo Python is installed:
python --version

:: Check if pip is installed
python -m pip --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Pip is not installed. Please install pip and try again.
    exit /b 1
)
echo Pip is installed:
python -m pip --version

:: Check if venv module is available
python -c "import venv" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python venv module is not available. Please install it and try again.
    exit /b 1
)
echo Python venv module is available

:: Create venvs directory if it doesn't exist
echo.
echo === Setting Up Virtual Environment ===
if not exist "%VENVS_DIR%" (
    echo Creating venvs directory...
    mkdir "%VENVS_DIR%"
    echo Created venvs directory at %VENVS_DIR%
) else (
    echo Venvs directory already exists at %VENVS_DIR%
)

:: Check if surge virtual environment already exists
if exist "%SURGE_VENV_DIR%" (
    echo Surge virtual environment already exists at %SURGE_VENV_DIR%
    set /p RECREATE="Do you want to recreate it? (y/n): "
    if /i "%RECREATE%"=="y" (
        echo Removing existing surge virtual environment...
        rmdir /s /q "%SURGE_VENV_DIR%"
        echo Removed existing surge virtual environment
    ) else (
        echo Using existing surge virtual environment
    )
)

:: Create surge virtual environment if it doesn't exist
if not exist "%SURGE_VENV_DIR%" (
    echo Creating surge virtual environment...
    python -m venv "%SURGE_VENV_DIR%"
    echo Created surge virtual environment at %SURGE_VENV_DIR%
)

:: Activate the virtual environment
echo Activating surge virtual environment...
call "%SURGE_VENV_DIR%\Scripts\activate.bat"
echo Activated surge virtual environment

:: Upgrade pip
echo.
echo === Installing Dependencies ===
echo Upgrading pip...
python -m pip install --upgrade pip
echo Upgraded pip

:: Install requirements
echo Installing requirements from %REQUIREMENTS_FILE%...
if exist "%REQUIREMENTS_FILE%" (
    python -m pip install -r "%REQUIREMENTS_FILE%"
    echo Installed all requirements
) else (
    echo Requirements file not found at %REQUIREMENTS_FILE%
    call "%SURGE_VENV_DIR%\Scripts\deactivate.bat"
    exit /b 1
)

:: Create necessary directories
echo.
echo === Creating Necessary Directories ===
if not exist "%PROJECT_ROOT%data\surge_analyzer\stock_lists" mkdir "%PROJECT_ROOT%data\surge_analyzer\stock_lists"
if not exist "%PROJECT_ROOT%results\surge_analyzer" mkdir "%PROJECT_ROOT%results\surge_analyzer"
echo Created data and results directories

:: Parse command line arguments
set "RUN_MODE=both"
set "EXTRA_ARGS="

:: Check for --dashboard or --pipeline flags
for %%a in (%*) do (
    if "%%a"=="--dashboard" (
        set "RUN_MODE=dashboard"
    ) else if "%%a"=="--pipeline" (
        set "RUN_MODE=pipeline"
    ) else (
        set "EXTRA_ARGS=!EXTRA_ARGS! %%a"
    )
)

:: Create logs directory if it doesn't exist
if not exist "%PROJECT_ROOT%logs\surge_analyzer" mkdir "%PROJECT_ROOT%logs\surge_analyzer"

:: Create a timestamp for the log file
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,8%_%dt:~8,6%"
set "LOG_FILE=%PROJECT_ROOT%logs\surge_analyzer\pipeline_run_%TIMESTAMP%.log"

:: Run the surge analyzer
echo.
echo === Running Surge Analyzer ===
if exist "%SURGE_SCRIPT%" (
    if "%RUN_MODE%"=="dashboard" (
        echo Running surge analyzer dashboard only...
        echo Running surge analyzer dashboard only... > "%LOG_FILE%"
        :: Run the script and capture output to both terminal and log file
        python "%SURGE_SCRIPT%" --disable-file-logging --mode dashboard %EXTRA_ARGS% 2>&1 | tee -a "%LOG_FILE%"
    ) else if "%RUN_MODE%"=="pipeline" (
        echo Running surge analyzer pipeline only...
        echo Running surge analyzer pipeline only... > "%LOG_FILE%"
        :: Run the script and capture output to both terminal and log file
        python "%SURGE_SCRIPT%" --disable-file-logging --mode pipeline %EXTRA_ARGS% 2>&1 | tee -a "%LOG_FILE%"
    ) else (
        echo Running surge analyzer pipeline and dashboard...
        echo Running surge analyzer pipeline and dashboard... > "%LOG_FILE%"
        :: Run the script and capture output to both terminal and log file
        python "%SURGE_SCRIPT%" --disable-file-logging --mode both %EXTRA_ARGS% 2>&1 | tee -a "%LOG_FILE%"
    )

    set SURGE_EXIT_CODE=%ERRORLEVEL%

    if %SURGE_EXIT_CODE% EQU 0 (
        echo Surge analyzer completed successfully
        echo Surge analyzer completed successfully >> "%LOG_FILE%"
    ) else (
        echo Surge analyzer failed with exit code %SURGE_EXIT_CODE%
        echo Surge analyzer failed with exit code %SURGE_EXIT_CODE% >> "%LOG_FILE%"
    )
) else (
    echo Surge analyzer script not found at %SURGE_SCRIPT%
    echo Surge analyzer script not found at %SURGE_SCRIPT% > "%LOG_FILE%"
    set SURGE_EXIT_CODE=1
)

:: Deactivate the virtual environment
echo.
echo === Cleaning Up ===
echo Deactivating surge virtual environment...
call "%SURGE_VENV_DIR%\Scripts\deactivate.bat"
echo Deactivated surge virtual environment

:: Final message
echo.
if %SURGE_EXIT_CODE% EQU 0 (
    echo === Surge Analyzer Completed Successfully ===
    echo Results are available in the results\surge_analyzer directory
    echo Log file is available at: %LOG_FILE%
) else (
    echo === Surge Analyzer Failed ===
    echo Please check the error messages above for more information
    echo Log file is available at: %LOG_FILE%
)

:: Pause to keep the window open
pause

exit /b %SURGE_EXIT_CODE%
