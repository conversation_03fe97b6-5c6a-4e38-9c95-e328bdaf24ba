@echo off
REM Run tests for the Surge Analyzer

REM Define paths
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%..\..\"
set "VENVS_DIR=%PROJECT_ROOT%venvs"
set "SURGE_VENV_DIR=%VENVS_DIR%\surge_analyzer"

REM Set up virtual environment if it doesn't exist
if not exist "%SURGE_VENV_DIR%" (
    echo Creating virtual environment...
    if not exist "%VENVS_DIR%" mkdir "%VENVS_DIR%"
    python -m venv "%SURGE_VENV_DIR%"
)

REM Activate virtual environment
call "%SURGE_VENV_DIR%\Scripts\activate.bat"

REM Install project and test dependencies
echo Installing project dependencies...
pip install -r "%PROJECT_ROOT%requirements\surge_analyzer\surge_analyzer_requirements.txt"

echo Installing test dependencies...
pip install -r "%PROJECT_ROOT%requirements\surge_analyzer\surge_analyzer_test_requirements.txt"

REM Run the tests
python "%PROJECT_ROOT%bin\surge_analyzer\run_surge_analyzer_tests.py" %*

REM Deactivate virtual environment
call "%SURGE_VENV_DIR%\Scripts\deactivate.bat"
