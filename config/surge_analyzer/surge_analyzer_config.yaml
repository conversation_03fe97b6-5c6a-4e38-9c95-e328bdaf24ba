# Consolidated Configuration for Stock Finder Pattern Recognition
# This file contains all configuration options for the application

# ===== DATA FETCHING PARAMETERS =====
data:
  # Directory to store downloaded data
  data_dir: "data/surge_analyzer"

  # Interval for stock data (1d = daily, 1wk = weekly, 1mo = monthly)
  interval: "1d"

  # Cache directory for stock lists
  stock_lists_cache_dir: "data/surge_analyzer/stock_lists"

# ===== STOCK SELECTION PARAMETERS =====
stock_selection:
  # Source of stock tickers
  source: "sp500"

  # Maximum number of stocks to analyze (0 for no limit)
  max_stocks: 0

  # Use cache for stock lists
  use_cache: true

# ===== ANALYSIS PARAMETERS =====
analysis:
  # Number of years to look back
  years_back: 1

  # Minimum percentage surge to identify
  min_surge_percent: 100.0

  # Price column to use for analysis (Close, Adj Close, etc.)
  price_col: "Close"

  # Batch size for processing stocks (optimized for performance)
  batch_size: 50

# ===== OUTPUT PARAMETERS =====
output:
  # Output format (console or csv)
  format: "csv"

  # Directory to save results
  results_dir: "results/surge_analyzer"

  # CSV file name (if format is csv)
  csv_path: "surge_analyzer_analysis.csv"

  # Whether to save detailed analysis for each stock
  save_details: true

# ===== MANUAL STOCK TICKERS =====
# Comma-separated list of stock tickers to analyze (only used if source is "manual")
tickers: "AAPL,MSFT,GOOGL,NVDA,TSLA"
